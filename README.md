# XAU积极套利交易系统

🚀 基于Bybit期货和MT5现货的黄金(XAU)自动套利交易系统

## 📊 策略特点

### 核心参数
- **进场阈值**: 0.3% (积极进场)
- **出场阈值**: 0.1% (快速止盈)
- **风控阈值**: 0.7% (异常保护)
- **杠杆倍数**: 20x
- **资金使用**: 每次2%保证金
- **最大仓位**: 5个并发套利
- **检测频率**: 每分钟

### 智能功能
- ✅ **市场检测**: 只在MT5市场开放时交易
- ✅ **动态资金**: 以较小账户余额为基准
- ✅ **完美对冲**: 确保两边名义价值相等
- ✅ **风控保护**: 异常价差自动平仓+24小时冷却
- ✅ **全面通知**: Telegram实时推送所有交易信息

## 🛠️ 安装部署

### 1. 环境要求
```bash
Python 3.8+
MetaTrader5 终端
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置设置
```bash
# 复制配置文件
cp config.env.example config.env

# 编辑配置文件，填入实际API密钥
nano config.env
```

### 4. 测试运行
```bash
# 测试策略参数
python test_new_strategy.py

# 测试MT5市场状态
python test_mt5_market.py
```

### 5. 启动系统
```bash
python start_new_strategy.py
```

## 📋 文件说明

### 核心模块
- `xau_arbitrage_trader.py` - 主套利引擎
- `bybit_futures_client.py` - Bybit期货客户端
- `bybit_mt5_client.py` - MT5客户端
- `telegram_notifier.py` - Telegram通知模块

### 配置文件
- `config.env` - 环境配置(需要自行创建)
- `config.env.example` - 配置文件示例

### 启动脚本
- `start_new_strategy.py` - 策略启动脚本

### 测试脚本
- `test_new_strategy.py` - 策略测试
- `test_mt5_market.py` - MT5市场测试

### 文档
- `STRATEGY_UPGRADE_SUMMARY.md` - 策略升级说明
- `DEPLOYMENT_CHECKLIST.md` - 部署清单

## ⚠️ 风险提示

1. **高频交易**: 此策略交易频率较高，请确保账户有足够资金
2. **杠杆风险**: 使用20倍杠杆，请谨慎管理风险
3. **市场风险**: 套利不等于无风险，请充分了解市场
4. **技术风险**: 请确保网络稳定，API连接正常
5. **资金安全**: 建议先用小资金测试

## 📞 支持

如有问题请查看日志文件或Telegram通知信息。

---
**免责声明**: 本系统仅供学习研究使用，使用者需自行承担交易风险。
