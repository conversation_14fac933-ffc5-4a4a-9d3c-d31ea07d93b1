#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
週末市場檢測測試
"""

import os
import sys
from datetime import datetime, timedelta
from unittest.mock import patch

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from xau_arbitrage_trader import XAUArbitrageTrader

def test_weekend_detection():
    """測試週末檢測功能"""
    print("🧪 測試週末市場檢測功能...")
    print("=" * 50)
    
    # 創建交易器實例（使用測試配置）
    trader = XAUArbitrageTrader()
    
    # 測試不同的日期
    test_dates = [
        # 週一到週五（工作日）
        datetime(2024, 1, 1),   # 週一
        datetime(2024, 1, 2),   # 週二  
        datetime(2024, 1, 3),   # 週三
        datetime(2024, 1, 4),   # 週四
        datetime(2024, 1, 5),   # 週五
        # 週末
        datetime(2024, 1, 6),   # 週六
        datetime(2024, 1, 7),   # 週日
    ]
    
    weekday_names = ['週一', '週二', '週三', '週四', '週五', '週六', '週日']
    
    for test_date in test_dates:
        with patch('xau_arbitrage_trader.datetime') as mock_datetime:
            mock_datetime.now.return_value = test_date
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            
            weekday = test_date.weekday()
            weekday_name = weekday_names[weekday]
            
            result = trader.check_weekend_status()
            expected = weekday not in [5, 6]  # 週六週日為False
            
            status = "✅" if result == expected else "❌"
            market_status = "開放" if result else "關閉"
            
            print(f"{status} {test_date.strftime('%Y-%m-%d')} ({weekday_name}): {market_status}")
    
    print("\n🧪 測試當前實際時間...")
    now = datetime.now()
    weekday = now.weekday()
    weekday_name = weekday_names[weekday]
    is_weekend = weekday in [5, 6]
    
    result = trader.check_weekend_status()
    market_status = "關閉" if is_weekend else "開放"
    
    print(f"📅 當前時間: {now.strftime('%Y-%m-%d %H:%M:%S')} ({weekday_name})")
    print(f"📊 市場狀態: {market_status}")
    print(f"🔍 檢測結果: {'正確' if result == (not is_weekend) else '錯誤'}")
    
    print("\n" + "=" * 50)
    print("✅ 週末檢測測試完成！")

if __name__ == "__main__":
    test_weekend_detection()
