#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新策略启动脚本
--------------
启动升级后的积极套利策略
"""

import os
import sys
from datetime import datetime
from xau_arbitrage_trader import XAUArbitrageTrader
from daily_scheduler import DailyScheduler

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🚀 XAU积极套利策略 v2.0")
    print("=" * 60)
    print("📊 策略参数:")
    print("   • 进场阈值: 0.3%")
    print("   • 出场阈值: 0.1%")
    print("   • 风控阈值: 0.7%")
    print("   • 资金使用: 每次2%，20倍杠杆")
    print("   • 最大仓位: 5个并发")
    print("   • 检测频率: 每分钟")
    print("   • 市场检测: MT5开放时才交易")
    print("=" * 60)

def check_config():
    """检查配置文件"""
    print("🔍 检查配置文件...")
    
    if not os.path.exists('config.env'):
        print("❌ 找不到 config.env 文件")
        return False
    
    # 检查必要的环境变量
    required_vars = [
        'BYBIT_API_KEY',
        'BYBIT_API_SECRET', 
        'BYBIT_MT5_LOGIN',
        'BYBIT_MT5_PASSWORD',
        'BYBIT_MT5_SERVER',
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_CHAT_ID'
    ]
    
    from dotenv import load_dotenv
    load_dotenv('config.env')
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少必要配置: {', '.join(missing_vars)}")
        return False
    
    print("✅ 配置文件检查通过")
    return True

def confirm_start():
    """确认启动"""
    print("\n⚠️  重要提醒:")
    print("1. 这是积极套利策略，交易频率较高")
    print("2. 每次使用2%资金，20倍杠杆，最多5个并发仓位")
    print("3. 请确保账户有足够余额")
    print("4. 建议先用小资金测试")
    print("5. 密切关注Telegram通知")
    print("6. 只在MT5市场开放时进行交易")
    
    while True:
        response = input("\n确认启动新策略? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            return True
        elif response in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y 或 n")

def main():
    """主函数"""
    print_banner()
    
    # 检查配置
    if not check_config():
        print("\n❌ 配置检查失败，请检查 config.env 文件")
        sys.exit(1)
    
    # 确认启动
    if not confirm_start():
        print("\n⏹️ 用户取消启动")
        sys.exit(0)
    
    print(f"\n🚀 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔄 正在初始化交易系统...")
    
    try:
        # 创建交易器实例
        trader = XAUArbitrageTrader()

        # 启动每日报告调度器（后台运行）
        daily_scheduler = DailyScheduler()
        scheduler_thread = daily_scheduler.run_in_background()
        print("📊 每日报告调度器已启动（每天8:00发送报告）")

        # 发送启动通知
        start_message = f"""
🚀 <b>XAU积极套利策略启动</b>

⏰ <b>启动时间:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 <b>策略参数:</b>
• 进场阈值: 0.3%
• 出场阈值: 0.1%
• 风控阈值: 0.7%
• 资金使用: 每次2%
• 杠杆倍数: 20x
• 最大仓位: 5个
• 检测频率: 每分钟
• 市场检测: MT5开放时才交易

🎯 <b>策略特点:</b>
• 积极开仓，每分钟检测
• 多仓位并发管理
• 智能风控保护
• 全面通知系统
• 每日8:00自动发送交易报告

✅ 系统已就绪，开始监控套利机会...
"""
        trader.notifier.send_message(start_message)
        
        # 启动套利系统
        trader.run_arbitrage_system(interval=60)
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户手动停止")
    except Exception as e:
        print(f"\n❌ 系统错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
