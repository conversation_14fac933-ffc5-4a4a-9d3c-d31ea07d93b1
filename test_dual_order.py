import time
from bybit_futures_client import BybitFuturesClient
from bybit_mt5_client import BybitMT5Client

# 初始化客戶端
bybit_client = BybitFuturesClient()
mt5_client = BybitMT5Client()

symbol_bybit = "XAUTUSDT"
symbol_mt5 = "XAUUSD+"

# 1. Bybit 下單
print("\n=== Bybit 下單 ===")
account_info = bybit_client.get_account_info()
wallet = account_info.get("list", [{}])[0]
available_balance = float(wallet.get("totalAvailableBalance", 0))
current_price = bybit_client.get_ticker_price(symbol_bybit)
min_qty = max(0.001, 5.0 / current_price) if current_price else 0.002
min_qty = round(min_qty, 3)
print(f"Bybit 下單數量: {min_qty}")
bybit_order = bybit_client.place_order(
    symbol=symbol_bybit,
    side="Buy",
    qty=min_qty,
    order_type="Market"
)
print(f"Bybit 下單回應: {bybit_order}")

# 2. MT5 下單
print("\n=== MT5 下單 ===")
mt5_order = None
used_filling_mode = None
if mt5_client.connect():
    import MetaTrader5 as mt5
    symbol_info = mt5.symbol_info(symbol_mt5)
    filling_modes = []
    if symbol_info is not None:
        if hasattr(symbol_info, 'filling_modes') and symbol_info.filling_modes:
            filling_modes = list(symbol_info.filling_modes)
        elif hasattr(symbol_info, 'filling_mode'):
            filling_modes = [symbol_info.filling_mode]
    # 若券商沒標註，全部都試
    try_modes = [0, 1, 2]
    for mode in try_modes:
        print(f"嘗試 filling mode: {mode}")
        mt5_order = mt5_client.place_order(
            symbol=symbol_mt5,
            order_type=0,
            volume=0.01,
            comment="DualTest",
            type_filling=mode
        )
        if mt5_order:
            used_filling_mode = mode
            print(f"✅ MT5 下單成功，filling mode={mode}")
            break
        else:
            print(f"❌ MT5 下單失敗，filling mode={mode}")
    print(f"MT5 下單回應: {mt5_order}")
    # 查詢持倉
    positions = mt5_client.get_positions(symbol_mt5)
    print(f"MT5 持倉: {positions}")
else:
    print("MT5 連接失敗")

# 3. 等待1秒
print("\n等待1秒後平倉...")
time.sleep(1)

# 4. Bybit 平倉（市價賣出）
print("\n=== Bybit 平倉 ===")
if bybit_order:
    close_order = bybit_client.place_order(
        symbol=symbol_bybit,
        side="Sell",
        qty=min_qty,
        order_type="Market"
    )
    print(f"Bybit 平倉回應: {close_order}")
else:
    print("Bybit 無下單，無法平倉")

# 5. MT5 平倉
print("\n=== MT5 平倉 ===")
if mt5_client.connect():
    positions = mt5_client.get_positions(symbol_mt5)
    for pos in positions:
        close_result = mt5_client.close_position(
            ticket=pos.get('ticket', 0),
            volume=pos.get('volume', 0)
        )
        print(f"MT5 平倉回應: {close_result}")
    mt5_client.disconnect()
else:
    print("MT5 連接失敗，無法平倉")

print("\n=== 測試結束 ===") 