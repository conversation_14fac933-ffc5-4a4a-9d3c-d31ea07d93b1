#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端稳定性测试
专门测试云端环境下的价格获取稳定性
"""

import os
import sys
import time
from datetime import datetime

# 模拟云端环境
os.environ['RAILWAY_ENVIRONMENT'] = 'production'

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cloud_price_manager import get_cloud_price_manager
from bybit_mt5_client_cloud import BybitMT5ClientCloud

def test_cloud_price_manager():
    """测试云端价格管理器"""
    print("=" * 60)
    print("🧪 测试云端价格管理器")
    print("=" * 60)
    
    manager = get_cloud_price_manager()
    
    # 测试多次价格获取
    success_count = 0
    total_tests = 10
    prices = []
    
    for i in range(total_tests):
        print(f"\n第 {i+1}/{total_tests} 次价格获取测试:")
        
        start_time = time.time()
        price = manager.get_mt5_equivalent_price()
        end_time = time.time()
        
        if price:
            success_count += 1
            prices.append(price)
            print(f"✅ 成功获取价格: {price:.2f} (耗时: {end_time-start_time:.2f}秒)")
        else:
            print(f"❌ 价格获取失败")
        
        # 短暂等待
        if i < total_tests - 1:
            time.sleep(2)
    
    # 统计结果
    success_rate = success_count / total_tests * 100
    print(f"\n📊 价格管理器测试结果:")
    print(f"   成功率: {success_rate:.1f}% ({success_count}/{total_tests})")
    
    if prices:
        avg_price = sum(prices) / len(prices)
        min_price = min(prices)
        max_price = max(prices)
        price_range = max_price - min_price
        
        print(f"   平均价格: {avg_price:.2f}")
        print(f"   价格范围: {min_price:.2f} - {max_price:.2f}")
        print(f"   价格波动: {price_range:.2f} ({price_range/avg_price*100:.2f}%)")
    
    # 获取价格管理器状态
    status = manager.get_price_status()
    print(f"\n🔍 价格管理器状态:")
    print(f"   缓存大小: {status['cache_size']}")
    print(f"   历史记录: {status['history_size']}")
    print(f"   可用价格源: {status['available_sources']}")
    print(f"   最近价格: {status['last_prices']}")
    
    return success_rate >= 80  # 80%以上成功率算通过

def test_cloud_mt5_client():
    """测试云端MT5客户端"""
    print("=" * 60)
    print("🧪 测试云端MT5客户端")
    print("=" * 60)
    
    client = BybitMT5ClientCloud()
    
    # 测试连接
    connected = client.connect()
    print(f"连接状态: {'✅ 成功' if connected else '❌ 失败'}")
    
    if not connected:
        return False
    
    # 测试多次价格获取
    success_count = 0
    total_tests = 5
    
    for i in range(total_tests):
        print(f"\n第 {i+1}/{total_tests} 次报价获取测试:")
        
        start_time = time.time()
        tick = client.get_tick("XAUUSD+")
        end_time = time.time()
        
        if tick and tick.get('bid', 0) > 0:
            success_count += 1
            print(f"✅ 成功获取报价:")
            print(f"   买价: {tick['bid']:.2f}")
            print(f"   卖价: {tick['ask']:.2f}")
            print(f"   点差: {tick['spread']:.2f}")
            print(f"   耗时: {end_time-start_time:.2f}秒")
        else:
            print(f"❌ 报价获取失败")
        
        if i < total_tests - 1:
            time.sleep(3)
    
    client.disconnect()
    
    success_rate = success_count / total_tests * 100
    print(f"\n📊 MT5客户端测试结果:")
    print(f"   成功率: {success_rate:.1f}% ({success_count}/{total_tests})")
    
    return success_rate >= 80

def test_price_consistency():
    """测试价格一致性"""
    print("=" * 60)
    print("🧪 测试价格一致性")
    print("=" * 60)
    
    manager = get_cloud_price_manager()
    client = BybitMT5ClientCloud()
    
    if not client.connect():
        print("❌ MT5客户端连接失败")
        return False
    
    # 同时获取价格并比较
    manager_price = manager.get_mt5_equivalent_price()
    client_price = client.get_current_price("XAUUSD+")
    
    print(f"价格管理器价格: {manager_price}")
    print(f"MT5客户端价格: {client_price}")
    
    if manager_price and client_price:
        price_diff = abs(manager_price - client_price)
        price_diff_pct = price_diff / manager_price * 100
        
        print(f"价格差异: {price_diff:.2f} ({price_diff_pct:.3f}%)")
        
        # 价格差异应该很小（因为使用相同的价格管理器）
        is_consistent = price_diff_pct < 0.1  # 0.1%以内算一致
        
        print(f"一致性检查: {'✅ 通过' if is_consistent else '❌ 失败'}")
        
        client.disconnect()
        return is_consistent
    else:
        print("❌ 无法获取价格进行比较")
        client.disconnect()
        return False

def test_error_recovery():
    """测试错误恢复能力"""
    print("=" * 60)
    print("🧪 测试错误恢复能力")
    print("=" * 60)
    
    manager = get_cloud_price_manager()
    
    # 模拟网络问题（通过修改请求超时）
    print("模拟网络延迟...")
    
    # 测试在网络问题下的表现
    recovery_count = 0
    test_count = 3
    
    for i in range(test_count):
        print(f"\n第 {i+1} 次错误恢复测试:")
        
        try:
            price = manager.get_mt5_equivalent_price()
            if price:
                recovery_count += 1
                print(f"✅ 成功恢复并获取价格: {price:.2f}")
            else:
                print("❌ 恢复失败")
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        time.sleep(2)
    
    recovery_rate = recovery_count / test_count * 100
    print(f"\n📊 错误恢复测试结果:")
    print(f"   恢复成功率: {recovery_rate:.1f}% ({recovery_count}/{test_count})")
    
    return recovery_rate >= 60  # 60%以上恢复率算通过

def main():
    """主测试函数"""
    print("🚀 开始云端稳定性测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"模拟环境: Railway云端环境")
    
    # 运行各项测试
    tests = [
        ("云端价格管理器", test_cloud_price_manager),
        ("云端MT5客户端", test_cloud_mt5_client),
        ("价格一致性", test_price_consistency),
        ("错误恢复能力", test_error_recovery),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 打印测试结果摘要
    print("\n" + "=" * 60)
    print("📊 云端稳定性测试结果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！云端环境应该非常稳定")
        print("\n🔧 云端稳定性特性:")
        print("1. ✅ 多重备用价格源")
        print("2. ✅ 智能价格缓存")
        print("3. ✅ 异常价格过滤")
        print("4. ✅ 自动错误恢复")
        print("5. ✅ 价格历史记录")
    elif passed >= total * 0.75:
        print("⚠️ 大部分测试通过，云端环境基本稳定")
    else:
        print("❌ 多项测试失败，云端环境可能不稳定")
    
    return passed >= total * 0.75  # 75%以上通过率算成功

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 云端稳定性测试完成，结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
