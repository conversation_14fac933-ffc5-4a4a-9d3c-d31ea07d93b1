#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端部署配置
自动检测运行环境并选择合适的客户端
"""

import os
import sys
import platform

def is_cloud_environment():
    """检测是否在云端环境"""
    # 检测常见的云端环境标识
    cloud_indicators = [
        'RAILWAY_ENVIRONMENT',  # Railway
        'HEROKU_APP_NAME',      # Heroku
        'VERCEL',               # Vercel
        'AWS_LAMBDA_FUNCTION_NAME',  # AWS Lambda
        'GOOGLE_CLOUD_PROJECT', # Google Cloud
        'AZURE_FUNCTIONS_ENVIRONMENT',  # Azure
        'RENDER',               # Render
        'FLY_APP_NAME',         # Fly.io
    ]
    
    # 检查环境变量
    for indicator in cloud_indicators:
        if os.getenv(indicator):
            return True
    
    # 检查操作系统
    if platform.system() != 'Windows':
        return True
    
    # 检查是否有MetaTrader5包
    try:
        import MetaTrader5
        return False
    except ImportError:
        return True

def get_mt5_client():
    """根据环境获取合适的MT5客户端"""
    if is_cloud_environment():
        print("🌐 检测到云端环境，使用云端兼容的MT5客户端")
        from bybit_mt5_client_cloud import BybitMT5ClientCloud
        return BybitMT5ClientCloud
    else:
        print("🖥️ 检测到本地Windows环境，使用标准MT5客户端")
        from bybit_mt5_client import BybitMT5Client
        return BybitMT5Client

def get_requirements_file():
    """根据环境获取合适的requirements文件"""
    if is_cloud_environment():
        return 'requirements-cloud.txt'
    else:
        return 'requirements.txt'

def print_environment_info():
    """打印环境信息"""
    print("=" * 50)
    print("🔍 环境检测结果")
    print("=" * 50)
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    print(f"是否云端环境: {'是' if is_cloud_environment() else '否'}")
    print(f"推荐requirements: {get_requirements_file()}")
    
    # 检查MetaTrader5可用性
    try:
        import MetaTrader5
        print("MetaTrader5包: ✅ 可用")
    except ImportError:
        print("MetaTrader5包: ❌ 不可用（云端环境正常）")
    
    print("=" * 50)

if __name__ == "__main__":
    print_environment_info()
