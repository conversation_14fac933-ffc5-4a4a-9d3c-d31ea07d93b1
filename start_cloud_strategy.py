#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端部署专用启动脚本
自动检测环境并使用合适的客户端
"""

import os
import sys
import time
import threading
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cloud_config import print_environment_info, is_cloud_environment
from xau_arbitrage_trader import XAUArbitrageTrader
from daily_scheduler import DailyScheduler
from telegram_notifier import TelegramNotifier

# 启动通知去重机制
STARTUP_NOTIFICATION_FILE = 'data/last_startup_notification.txt'

def should_send_startup_notification():
    """检查是否应该发送启动通知（避免重复发送）"""
    try:
        # 确保数据目录存在
        os.makedirs('data', exist_ok=True)

        if os.path.exists(STARTUP_NOTIFICATION_FILE):
            with open(STARTUP_NOTIFICATION_FILE, 'r') as f:
                last_notification_time = datetime.fromisoformat(f.read().strip())

            # 如果距离上次通知不到5分钟，则不发送
            if datetime.now() - last_notification_time < timedelta(minutes=5):
                print("⏭️ 跳过启动通知（距离上次通知不到5分钟）")
                return False

        return True
    except Exception as e:
        print(f"⚠️ 检查启动通知状态失败: {e}")
        return True  # 出错时还是发送通知

def record_startup_notification():
    """记录启动通知时间"""
    try:
        os.makedirs('data', exist_ok=True)
        with open(STARTUP_NOTIFICATION_FILE, 'w') as f:
            f.write(datetime.now().isoformat())
    except Exception as e:
        print(f"⚠️ 记录启动通知时间失败: {e}")

def send_startup_notification():
    """发送启动通知（带去重机制）"""
    if not should_send_startup_notification():
        return

    notifier = TelegramNotifier()

    env_type = "云端环境" if is_cloud_environment() else "本地环境"

    message = f"""🚀 XAU套利系统启动 🚀

🌐 运行环境: {env_type}
⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 策略特点:
• 积极开仓，每分钟检测
• 多仓位并发管理
• 智能风控保护
• 全面通知系统
• 每日8:00自动发送交易报告
• 云端兼容部署

✅ 系统已就绪，开始监控套利机会..."""

    success = notifier.send_message(message)
    if success:
        print("✅ 启动通知发送成功")
        record_startup_notification()
    else:
        print("❌ 启动通知发送失败")

def start_daily_scheduler():
    """启动每日报告调度器"""
    try:
        scheduler = DailyScheduler()
        scheduler_thread = threading.Thread(target=scheduler.start_scheduler, daemon=True)
        scheduler_thread.start()
        print("✅ 每日报告调度器已启动")
        return True
    except Exception as e:
        print(f"❌ 每日报告调度器启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 XAU套利系统 - 云端部署版")
    print("=" * 60)
    
    # 打印环境信息
    print_environment_info()
    
    try:
        # 发送启动通知
        send_startup_notification()
        
        # 启动每日报告调度器
        start_daily_scheduler()
        
        # 创建交易器实例
        print("🔧 初始化交易系统...")
        trader = XAUArbitrageTrader()
        
        print("✅ 交易系统初始化完成")
        print("🎯 开始监控套利机会...")
        print("=" * 60)
        
        # 开始交易循环
        trader.run_arbitrage_system()
        
    except KeyboardInterrupt:
        print("\n⚠️ 收到停止信号，正在安全关闭系统...")
        
        # 发送停止通知
        try:
            notifier = TelegramNotifier()
            stop_message = f"""⚠️ XAU套利系统停止 ⚠️

⏰ 停止时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📊 系统状态: 手动停止

系统已安全关闭。"""
            notifier.send_message(stop_message)
        except:
            pass
        
        print("✅ 系统已安全关闭")
        
    except Exception as e:
        print(f"❌ 系统运行出错: {e}")
        
        # 发送错误通知
        try:
            notifier = TelegramNotifier()
            error_message = f"""🚨 XAU套利系统异常 🚨

⏰ 错误时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📊 错误信息: {str(e)}

请检查系统状态。"""
            notifier.send_message(error_message)
        except:
            pass
        
        raise

if __name__ == "__main__":
    main()
