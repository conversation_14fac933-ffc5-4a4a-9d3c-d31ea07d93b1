#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MT5市场状态测试脚本
"""

from bybit_mt5_client import BybitMT5Client
import MetaTrader5 as mt5

def test_mt5_market_status():
    """测试MT5市场状态"""
    print("🧪 测试MT5市场状态...")
    
    client = BybitMT5Client()
    
    if not client.connect():
        print("❌ 无法连接MT5")
        return
    
    # 获取XAUUSD+的详细信息
    symbol_info = client.get_symbol_info("XAUUSD+")
    
    if symbol_info:
        print("✅ XAUUSD+ 信息获取成功:")
        for key, value in symbol_info.items():
            print(f"   {key}: {value}")
        
        # 检查交易模式
        trade_mode = symbol_info.get('trade_mode', 0)
        print(f"\n📊 交易模式分析:")
        print(f"   trade_mode: {trade_mode}")
        
        # MT5交易模式说明
        trade_modes = {
            0: "禁用",
            1: "仅多头",
            2: "仅空头", 
            3: "仅平仓",
            4: "完全交易"
        }
        
        mode_desc = trade_modes.get(trade_mode, "未知")
        print(f"   模式说明: {mode_desc}")
        
        # 判断是否可以交易
        can_trade = trade_mode in [1, 2, 4]  # 允许开仓的模式
        print(f"   可以交易: {'是' if can_trade else '否'}")
        
    else:
        print("❌ 无法获取XAUUSD+信息")
    
    client.disconnect()

def test_direct_mt5():
    """直接使用MT5 API测试"""
    print("\n🧪 直接使用MT5 API测试...")
    
    # 初始化MT5
    if not mt5.initialize():
        print("❌ MT5初始化失败")
        return
    
    # 登录
    login = 5380217
    password = "Ricky131364517!"
    server = "Bybit-Live-2"
    
    if not mt5.login(login, password=password, server=server):
        print(f"❌ MT5登录失败: {mt5.last_error()}")
        mt5.shutdown()
        return
    
    print("✅ MT5直接连接成功")
    
    # 选择XAUUSD+
    if not mt5.symbol_select("XAUUSD+", True):
        print(f"❌ 无法选择XAUUSD+: {mt5.last_error()}")
    else:
        print("✅ XAUUSD+选择成功")
    
    # 获取符号信息
    symbol_info = mt5.symbol_info("XAUUSD+")
    if symbol_info:
        print("✅ 符号信息:")
        print(f"   名称: {symbol_info.name}")
        print(f"   买价: {symbol_info.bid}")
        print(f"   卖价: {symbol_info.ask}")
        print(f"   交易模式: {symbol_info.trade_mode}")
        print(f"   可见: {symbol_info.visible}")
        print(f"   选中: {symbol_info.select}")
        
        # 检查市场时间
        market_info = mt5.symbol_info_tick("XAUUSD+")
        if market_info:
            print(f"   最新价格: {market_info.last}")
            print(f"   时间: {market_info.time}")
    
    mt5.shutdown()

if __name__ == "__main__":
    test_mt5_market_status()
    test_direct_mt5()
