#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试云端价格获取修复
验证云端版本是否能正确获取Bybit XAUTUSDT和MT5等效价格
"""

import os
import sys
import requests
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_bybit_futures_price():
    """测试Bybit永续合约价格获取"""
    print("=" * 60)
    print("🧪 测试Bybit永续合约XAUTUSDT价格获取")
    print("=" * 60)
    
    try:
        response = requests.get(
            'https://api.bybit.com/v5/market/tickers',
            params={'category': 'linear', 'symbol': 'XAUTUSDT'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('retCode') == 0 and data.get('result', {}).get('list'):
                ticker = data['result']['list'][0]
                price = float(ticker['lastPrice'])
                
                print(f"✅ Bybit永续合约XAUTUSDT价格: ${price:.2f}")
                
                if price > 1000 and price < 5000:
                    print(f"✅ 价格在合理范围内")
                    return price
                else:
                    print(f"❌ 价格超出合理范围: {price}")
                    return None
            else:
                print(f"❌ Bybit API返回错误: {data}")
                return None
        else:
            print(f"❌ Bybit API请求失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取Bybit永续合约价格异常: {e}")
        return None

def test_cloud_mt5_client():
    """测试云端MT5客户端价格获取"""
    print("=" * 60)
    print("🧪 测试云端MT5客户端价格获取")
    print("=" * 60)
    
    try:
        from bybit_mt5_client_cloud import BybitMT5ClientCloud
        
        client = BybitMT5ClientCloud()
        
        # 测试连接
        if client.connect():
            print("✅ 云端MT5客户端连接成功")
            
            # 测试价格获取
            price = client.get_current_price("XAUUSD+")
            if price:
                print(f"✅ MT5等效价格: ${price:.2f}")
                
                if price > 1000 and price < 5000:
                    print(f"✅ 价格在合理范围内")
                    client.disconnect()
                    return price
                else:
                    print(f"❌ 价格超出合理范围: {price}")
                    client.disconnect()
                    return None
            else:
                print("❌ 无法获取MT5等效价格")
                client.disconnect()
                return None
        else:
            print("❌ 云端MT5客户端连接失败")
            return None
    except Exception as e:
        print(f"❌ 云端MT5客户端测试异常: {e}")
        return None

def test_price_spread_calculation():
    """测试价差计算"""
    print("=" * 60)
    print("🧪 测试价差计算")
    print("=" * 60)
    
    # 获取Bybit永续合约价格
    bybit_price = test_bybit_futures_price()
    if not bybit_price:
        print("❌ 无法获取Bybit价格，跳过价差计算测试")
        return False
    
    # 获取MT5等效价格
    mt5_price = test_cloud_mt5_client()
    if not mt5_price:
        print("❌ 无法获取MT5价格，跳过价差计算测试")
        return False
    
    # 计算价差
    spread = mt5_price - bybit_price
    percentage = (spread / bybit_price) * 100
    direction = 'MT5 > Bybit' if spread > 0 else 'Bybit > MT5'
    
    print(f"\n📊 价差计算结果:")
    print(f"   Bybit XAUTUSDT: ${bybit_price:.2f}")
    print(f"   MT5 XAUUSD+等效: ${mt5_price:.2f}")
    print(f"   绝对价差: ${spread:.2f}")
    print(f"   百分比价差: {percentage:.4f}%")
    print(f"   价差方向: {direction}")
    
    # 检查价差是否合理
    abs_percentage = abs(percentage)
    if abs_percentage < 2.0:  # 价差应该小于2%
        print(f"✅ 价差在合理范围内 ({abs_percentage:.4f}% < 2%)")
        return True
    else:
        print(f"❌ 价差过大 ({abs_percentage:.4f}% >= 2%)")
        return False

def test_cloud_price_manager():
    """测试云端价格管理器"""
    print("=" * 60)
    print("🧪 测试云端价格管理器")
    print("=" * 60)
    
    try:
        from cloud_price_manager import CloudPriceManager
        
        manager = CloudPriceManager()
        
        # 测试价格获取
        price = manager.get_mt5_equivalent_price()
        if price:
            print(f"✅ 价格管理器获取价格: ${price:.2f}")
            
            if price > 1000 and price < 5000:
                print(f"✅ 价格在合理范围内")
                return True
            else:
                print(f"❌ 价格超出合理范围: {price}")
                return False
        else:
            print("❌ 价格管理器无法获取价格")
            return False
    except Exception as e:
        print(f"❌ 价格管理器测试异常: {e}")
        return False

def test_arbitrage_trader_cloud():
    """测试套利交易者云端版本"""
    print("=" * 60)
    print("🧪 测试套利交易者云端版本")
    print("=" * 60)
    
    try:
        from xau_arbitrage_trader import XAUArbitrageTrader
        
        trader = XAUArbitrageTrader()
        
        # 测试价格获取
        prices = trader.get_current_prices()
        print(f"获取到的价格: {prices}")
        
        if 'bybit' in prices and 'mt5' in prices:
            bybit_price = prices['bybit']
            mt5_price = prices['mt5']
            
            print(f"✅ Bybit价格: ${bybit_price:.2f}")
            print(f"✅ MT5价格: ${mt5_price:.2f}")
            
            # 测试价差计算
            spread_info = trader.calculate_spread(bybit_price, mt5_price)
            
            print(f"📊 价差信息:")
            print(f"   绝对价差: ${spread_info['absolute']:.2f}")
            print(f"   百分比价差: {spread_info['percentage']:.4f}%")
            print(f"   价差方向: {spread_info['direction']}")
            
            # 检查价差是否合理
            abs_percentage = abs(spread_info['percentage'])
            if abs_percentage < 2.0:
                print(f"✅ 价差在合理范围内 ({abs_percentage:.4f}% < 2%)")
                return True
            else:
                print(f"❌ 价差过大 ({abs_percentage:.4f}% >= 2%)")
                return False
        else:
            print("❌ 无法获取完整的价格数据")
            return False
    except Exception as e:
        print(f"❌ 套利交易者测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始云端价格获取修复测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行各项测试
    tests = [
        ("Bybit永续合约价格", test_bybit_futures_price),
        ("云端价格管理器", test_cloud_price_manager),
        ("云端MT5客户端", test_cloud_mt5_client),
        ("价差计算", test_price_spread_calculation),
        ("套利交易者云端版本", test_arbitrage_trader_cloud),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = bool(result)
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 打印测试结果摘要
    print("\n" + "=" * 60)
    print("📊 云端价格获取修复测试结果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！云端价格获取问题已修复")
        print("\n🔧 修复内容:")
        print("1. ✅ 简化云端价格管理器，只使用Bybit永续合约价格")
        print("2. ✅ 移除不必要的外部价格源（CoinGecko、Yahoo Finance）")
        print("3. ✅ 直接使用Bybit永续合约价格作为MT5等效价格")
        print("4. ✅ 确保价差计算逻辑与本地版本一致")
    else:
        print("⚠️ 部分测试失败，可能仍有问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 测试完成，结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
