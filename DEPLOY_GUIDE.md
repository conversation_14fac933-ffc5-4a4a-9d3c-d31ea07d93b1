# XAU套利模擬交易系統 - 雲端部署指南

## 🎯 部署概述

這個指南將幫助您把XAU套利模擬交易系統部署到Amazon Lightsail上，實現24/7雲端運行。

## 📋 部署前準備

### 1. 確認本地文件
確保以下文件都在您的項目目錄中：
```
xau_arbitrage/
├── simulation_trader.py          # 模擬交易核心
├── cloud_simulation_runner.py    # 雲端運行器
├── bybit_futures_client.py       # Bybit客戶端
├── cloud_config.py               # 雲端配置
├── telegram_notifier.py          # Telegram通知
├── config.env                    # 環境配置
├── requirements.txt              # Python依賴
├── deploy_setup.sh               # 部署腳本
└── DEPLOY_GUIDE.md               # 本指南
```

### 2. 檢查config.env
確保您的`config.env`包含所有必要的API密鑰：
```env
# Bybit API
BYBIT_API_KEY=your_api_key
BYBIT_API_SECRET=your_api_secret

# Telegram通知
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# MT5設置（雲端版本）
MT5_LOGIN=your_mt5_login
MT5_PASSWORD=your_mt5_password
MT5_SERVER=your_mt5_server
```

## 🚀 部署步驟

### 步驟1: 準備Lightsail實例

1. **創建實例**
   - 登入AWS Lightsail控制台
   - 選擇"Ubuntu 22.04 LTS"
   - 選擇適當的實例大小（建議$10/月的方案）
   - 設置靜態IP

2. **下載SSH密鑰**
   - 在Lightsail控制台 → Account → SSH Keys
   - 下載您所在區域的預設密鑰（.pem文件）
   - 保存到本地，例如：`~/keys/LightsailDefaultKey.pem`

3. **設置密鑰權限**
   ```bash
   chmod 600 ~/keys/LightsailDefaultKey.pem
   ```

### 步驟2: 上傳文件到雲端

使用scp命令上傳整個項目：

```bash
# 替換YOUR_STATIC_IP為您的Lightsail實例IP
scp -i ~/keys/LightsailDefaultKey.pem -r ./xau_arbitrage ubuntu@YOUR_STATIC_IP:/home/<USER>/
```

### 步驟3: 連接並設置環境

1. **SSH連接到實例**
   ```bash
   ssh -i ~/keys/LightsailDefaultKey.pem ubuntu@YOUR_STATIC_IP
   ```

2. **運行部署腳本**
   ```bash
   cd ~/xau_arbitrage
   chmod +x deploy_setup.sh
   ./deploy_setup.sh
   ```

### 步驟4: 測試和啟動

1. **測試運行**
   ```bash
   cd ~/xau_arbitrage
   source venv/bin/activate
   python3 cloud_simulation_runner.py
   ```
   按Ctrl+C停止測試

2. **啟動系統服務**
   ```bash
   sudo systemctl start xau-simulation
   sudo systemctl status xau-simulation
   ```

3. **查看實時日誌**
   ```bash
   sudo journalctl -u xau-simulation -f
   ```

## 📊 監控和管理

### 查看服務狀態
```bash
sudo systemctl status xau-simulation
```

### 重啟服務
```bash
sudo systemctl restart xau-simulation
```

### 停止服務
```bash
sudo systemctl stop xau-simulation
```

### 查看日誌
```bash
# 查看系統日誌
sudo journalctl -u xau-simulation -f

# 查看應用日誌
tail -f ~/xau_arbitrage/cloud_simulation.log
```

### 查看模擬數據
```bash
# 查看交易記錄
cat ~/xau_arbitrage/data/simulation_results.json

# 查看績效數據
cat ~/xau_arbitrage/data/simulation_performance.csv
```

## 🔧 常見問題解決

### 1. 上傳失敗
- 檢查SSH密鑰路徑和權限
- 確認Lightsail實例IP正確
- 確認實例正在運行

### 2. 依賴安裝失敗
```bash
# 更新系統
sudo apt update && sudo apt upgrade -y

# 重新安裝依賴
cd ~/xau_arbitrage
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 3. 服務啟動失敗
```bash
# 檢查服務狀態
sudo systemctl status xau-simulation

# 查看詳細錯誤
sudo journalctl -u xau-simulation --no-pager
```

### 4. API連接問題
- 檢查config.env中的API密鑰
- 確認網絡連接正常
- 檢查防火牆設置

## 🛡️ 安全建議

1. **定期備份**
   ```bash
   # 備份交易數據
   tar -czf backup_$(date +%Y%m%d).tar.gz ~/xau_arbitrage/data/
   ```

2. **監控資源使用**
   ```bash
   # 查看系統資源
   htop
   df -h
   free -h
   ```

3. **定期更新**
   ```bash
   # 更新系統
   sudo apt update && sudo apt upgrade -y
   
   # 更新Python依賴
   cd ~/xau_arbitrage
   source venv/bin/activate
   pip install --upgrade -r requirements.txt
   ```

## 📈 性能優化

### 調整檢查間隔
編輯`cloud_simulation_runner.py`中的參數：
```python
check_interval = 300  # 5分鐘檢查一次（可調整）
save_interval = 3600  # 1小時保存一次（可調整）
```

### 監控系統資源
```bash
# 安裝監控工具
sudo apt install htop iotop

# 查看進程
htop
```

## 🎯 部署完成檢查清單

- [ ] Lightsail實例創建並運行
- [ ] SSH密鑰下載並設置權限
- [ ] 項目文件成功上傳
- [ ] 部署腳本執行成功
- [ ] Python依賴安裝完成
- [ ] 系統服務創建並啟動
- [ ] 日誌顯示正常運行
- [ ] Telegram通知正常工作
- [ ] 模擬交易數據正常記錄

**恭喜！您的XAU套利模擬交易系統現在已在雲端24/7運行！** 🎉
