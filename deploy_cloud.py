#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端部署专用启动脚本
确保使用正确的依赖文件
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def install_cloud_dependencies():
    """安装云端专用依赖"""
    print("🔧 安装云端专用依赖...")
    
    try:
        # 确保使用云端专用的requirements文件
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements-cloud.txt'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 云端依赖安装成功")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 依赖安装超时")
        return False
    except Exception as e:
        print(f"❌ 依赖安装异常: {e}")
        return False

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    required_env_vars = [
        'BYBIT_API_KEY',
        'BYBIT_API_SECRET', 
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_CHAT_ID'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ 环境变量配置完整")
    return True

def start_trading_system():
    """启动交易系统"""
    print("🚀 启动XAU套利交易系统...")
    
    try:
        # 添加当前目录到Python路径
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # 导入并启动系统
        from start_cloud_strategy import main
        main()
        
    except KeyboardInterrupt:
        print("\n⚠️ 收到停止信号")
        return
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        raise

def main():
    """主函数"""
    print("=" * 60)
    print("🌐 XAU套利系统 - 云端部署启动器")
    print("=" * 60)
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 安装依赖
    if not install_cloud_dependencies():
        print("❌ 依赖安装失败，退出部署")
        sys.exit(1)
    
    # 2. 检查环境
    if not check_environment():
        print("❌ 环境检查失败，退出部署")
        sys.exit(1)
    
    # 3. 启动系统
    print("✅ 预检查完成，启动交易系统...")
    time.sleep(2)
    
    start_trading_system()

if __name__ == "__main__":
    main()
