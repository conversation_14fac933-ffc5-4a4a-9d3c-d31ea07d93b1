#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試10%保證金限制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from xau_arbitrage_trader import XAUArbitrageTrader

def test_10_percent_margin():
    """測試10%保證金限制"""
    print("🧪 測試10%保證金限制")
    print("=" * 50)
    
    # 創建交易器實例
    trader = XAUArbitrageTrader()
    
    # 檢查設定
    print(f"📊 當前設定:")
    print(f"   - 單次保證金比例: {trader.position_size_ratio:.1%}")
    print(f"   - 最大總曝險: {trader.max_total_exposure:.1%}")
    print(f"   - 槓桿倍數: {trader.leverage}x")
    print(f"   - 最大倉位數: {trader.max_positions}")
    
    # 獲取當前價格
    print("\n📈 獲取當前價格...")
    prices = trader.get_current_prices()
    if not prices or len(prices) < 2:
        print("❌ 無法獲取價格")
        return
    
    print(f"   - Bybit XAUTUSDT: {prices['bybit']:.2f}")
    print(f"   - MT5 XAUUSD+: {prices['mt5']:.2f}")
    
    # 計算價差
    spread_info = trader.calculate_spread(prices['bybit'], prices['mt5'])
    print(f"   - 價差: {spread_info['absolute']:.2f} ({spread_info['percentage']:.3f}%)")
    
    # 計算倉位大小
    print("\n💰 計算倉位大小...")
    position_sizes = trader.calculate_position_sizes(prices['bybit'], prices['mt5'])
    if not position_sizes:
        print("❌ 倉位大小計算失敗")
        return
    
    print(f"   - Bybit數量: {position_sizes['bybit_qty']:.6f}")
    print(f"   - MT5數量: {position_sizes['mt5_qty']:.2f}")
    print(f"   - 單邊保證金: {position_sizes['margin_per_side']:.2f}")
    print(f"   - 名義價值: {position_sizes['position_value']:.2f}")
    print(f"   - Bybit名義價值: {position_sizes['bybit_nominal']:.6f}")
    print(f"   - MT5名義價值: {position_sizes['mt5_nominal']:.6f}")
    
    # 檢查套利條件
    print("\n🔍 檢查套利條件...")
    can_trade = trader.check_arbitrage_conditions(spread_info, 0.0)
    print(f"   - 可以交易: {can_trade}")
    
    # 計算總曝險
    print("\n📊 計算總曝險...")
    total_exposure = trader.calculate_total_exposure()
    print(f"   - 當前總曝險: {total_exposure:.2%}")
    print(f"   - 最大允許曝險: {trader.max_total_exposure:.2%}")
    print(f"   - 是否超過限制: {total_exposure >= trader.max_total_exposure}")
    
    # 模擬多次交易檢查曝險限制
    print("\n🔄 模擬多次交易檢查曝險限制...")
    for i in range(3):
        print(f"\n   第{i+1}次交易:")
        
        # 檢查是否可以交易
        can_trade = trader.check_arbitrage_conditions(spread_info, 0.0)
        print(f"   - 可以交易: {can_trade}")
        
        if can_trade:
            # 模擬添加交易
            trade_id = f"test_{i+1}"
            trader.active_trades[trade_id] = {
                'total_margin': position_sizes['margin_per_side'] * 2,  # 兩邊保證金
                'position_value': position_sizes['position_value']
            }
            
            # 重新計算總曝險
            new_exposure = trader.calculate_total_exposure()
            print(f"   - 新增交易後總曝險: {new_exposure:.2%}")
            print(f"   - 是否超過限制: {new_exposure >= trader.max_total_exposure}")
        else:
            print("   - 無法交易，跳過")
    
    print("\n✅ 測試完成")

if __name__ == "__main__":
    test_10_percent_margin() 