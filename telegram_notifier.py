#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram 通知模組
----------------
發送交易記錄、套利機會等通知到 Telegram
"""

import os
import requests
from datetime import datetime
from typing import Optional, Dict, Any
from dotenv import load_dotenv

load_dotenv('config.env')

class TelegramNotifier:
    def __init__(self):
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.chat_id = os.getenv('TELEGRAM_CHAT_ID')
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        
    def send_message(self, message: str, parse_mode: str = "HTML") -> bool:
        """發送訊息到 Telegram"""
        if not self.bot_token or not self.chat_id:
            print("❌ Telegram 設定不完整")
            return False
        
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": parse_mode
            }
            
            response = requests.post(url, data=data, timeout=10)
            if response.status_code == 200:
                return True
            else:
                print(f"❌ Telegram 發送失敗: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Telegram 發送錯誤: {e}")
            return False
    
    def send_arbitrage_opportunity(self, bybit_price: float, mt5_price: float, 
                                 spread: float, percentage_spread: float, 
                                 funding_rate: float) -> bool:
        """發送套利機會通知"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        message = (
            f"🚨 <b>套利機會偵測</b> 🚨\n"
            f"⏰ <b>時間:</b> {timestamp}\n"
            f"💰 <b>Bybit XAUTUSDT:</b> {bybit_price:.2f}\n"
            f"🏆 <b>MT5 XAUUSD+:</b> {mt5_price:.2f}\n"
            f"📊 <b>絕對價差:</b> {spread:.2f}\n"
            f"📈 <b>百分比價差:</b> {percentage_spread:.3f}%\n"
            f"💸 <b>資金費率:</b> {funding_rate:.6f}\n"
            f"🎯 <b>套利條件:</b>\n"
            f"• 價差 > 0.3%: {'✅' if abs(percentage_spread) > 0.3 else '❌'}\n"
            f"• 資金費率檢查: {'✅' if funding_rate < 0 else '❌'}\n"
            f"📋 <b>建議策略:</b>\n"
            f"{'• 賣出 MT5 XAUUSD+ → 買入 Bybit XAUTUSDT' if percentage_spread > 0 else '• 買入 MT5 XAUUSD+ → 賣出 Bybit XAUTUSDT'}"
        )
        return self.send_message(message)
    
    def send_trade_execution(self, trade_info: Dict[str, Any]) -> bool:
        """發送交易執行通知"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        message = f"""
🎯 <b>套利交易執行</b> 🎯

⏰ <b>時間:</b> {timestamp}
📊 <b>交易 ID:</b> {trade_info.get('trade_id', 'N/A')}

<b>Bybit 永續合約:</b>
• 方向: {trade_info.get('bybit_side', 'N/A')}
• 數量: {trade_info.get('bybit_qty', 0):.3f}
• 價格: {trade_info.get('bybit_price', 0):.2f}
• 保證金: {trade_info.get('bybit_margin', 0):.2f} USDT
• 手續費: {trade_info.get('bybit_fee', 0):.4f} USDT

<b>MT5 現貨:</b>
• 方向: {trade_info.get('mt5_side', 'N/A')}
• 數量: {trade_info.get('mt5_qty', 0):.2f}
• 價格: {trade_info.get('mt5_price', 0):.2f}
• 保證金: {trade_info.get('mt5_margin', 0):.2f} USD
• 手續費: {trade_info.get('mt5_fee', 0):.4f} USD

💰 <b>總投資:</b> {trade_info.get('total_margin', 0):.2f}
📈 <b>預期收益:</b> {trade_info.get('expected_profit', 0):.2f}
"""
        
        return self.send_message(message)
    
    def send_trade_close(self, close_info: Dict[str, Any]) -> bool:
        """發送平倉通知（增強版）"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        message = f"""
✅ <b>套利交易平倉完成</b> ✅

⏰ <b>時間:</b> {timestamp}
📊 <b>交易 ID:</b> {close_info.get('trade_id', 'N/A')}

<b>📈 Bybit 永續合約:</b>
• 開倉價格: {close_info.get('bybit_open_price', 0):.2f}
• 平倉價格: {close_info.get('bybit_close_price', 0):.2f}
• 數量: {close_info.get('bybit_qty', 0):.3f}
• 收益: {close_info.get('bybit_pnl', 0):.2f} USDT
• 手續費: {close_info.get('bybit_total_fee', 0):.4f} USDT

<b>🏆 MT5 現貨:</b>
• 開倉價格: {close_info.get('mt5_open_price', 0):.2f}
• 平倉價格: {close_info.get('mt5_close_price', 0):.2f}
• 數量: {close_info.get('mt5_qty', 0):.2f}
• 收益: {close_info.get('mt5_pnl', 0):.2f} USD
• 手續費: {close_info.get('mt5_total_fee', 0):.4f} USD

<b>💰 收益總結:</b>
• 總手續費: {close_info.get('total_fees', 0):.4f}
• 毛收益: {close_info.get('gross_profit', 0):.2f}
• 淨收益: {close_info.get('net_profit', 0):.2f}
• 收益率: {close_info.get('roi', 0):.3f}%
• 持倉時間: {close_info.get('duration', 'N/A')}

<b>💳 帳戶餘額:</b>
• Bybit 餘額: {close_info.get('bybit_balance_after', 0):.2f} USDT
• MT5 餘額: {close_info.get('mt5_balance_after', 0):.2f} USD
• 總價值: {close_info.get('total_value_usd', 0):.2f} USD

<b>📊 套利統計:</b>
• 今日套利次數: {close_info.get('daily_trades', 0)}
• 今日淨收益: {close_info.get('daily_profit', 0):.2f} USD
• 成功率: {close_info.get('success_rate', 0):.1f}%
"""

        return self.send_message(message)

    def send_daily_report(self, report_data: Dict[str, Any]) -> bool:
        """發送每日交易報告"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        report_date = report_data.get('date', datetime.now().strftime('%Y-%m-%d'))

        message = f"""
📊 <b>每日套利交易報告</b> 📊

📅 <b>報告日期:</b> {report_date}
⏰ <b>生成時間:</b> {timestamp}

<b>🎯 交易統計:</b>
• 總交易次數: {report_data.get('total_trades', 0)}
• 成功交易: {report_data.get('successful_trades', 0)}
• 失敗交易: {report_data.get('failed_trades', 0)}
• 成功率: {report_data.get('success_rate', 0):.1f}%

<b>💰 收益統計:</b>
• 總毛收益: {report_data.get('gross_profit', 0):.2f} USD
• 總手續費: {report_data.get('total_fees', 0):.4f} USD
• 總淨收益: {report_data.get('net_profit', 0):.2f} USD
• 平均每筆收益: {report_data.get('avg_profit_per_trade', 0):.2f} USD
• 最大單筆收益: {report_data.get('max_profit', 0):.2f} USD
• 最小單筆收益: {report_data.get('min_profit', 0):.2f} USD

<b>⏱️ 時間統計:</b>
• 平均持倉時間: {report_data.get('avg_duration', 'N/A')}
• 最長持倉時間: {report_data.get('max_duration', 'N/A')}
• 最短持倉時間: {report_data.get('min_duration', 'N/A')}

<b>�� 帳戶狀況:</b>
• Bybit 餘額: {report_data.get('bybit_balance', 0):.2f} USDT
• MT5 餘額: {report_data.get('mt5_balance', 0):.2f} USD

<b>📈 價差統計:</b>
• 平均進場價差: {report_data.get('avg_entry_spread', 0):.3f}%
• 平均出場價差: {report_data.get('avg_exit_spread', 0):.3f}%
• 最大價差: {report_data.get('max_spread', 0):.3f}%

<b>🔍 風控統計:</b>
• 風控觸發次數: {report_data.get('risk_triggers', 0)}
• 最大回撤: {report_data.get('max_drawdown', 0):.2f} USD
• 當前活躍倉位: {report_data.get('active_positions', 0)}

<b>📋 交易明細:</b>
{report_data.get('trade_details', '今日無交易記錄')}
"""

        return self.send_message(message)

    def send_error_notification(self, error_msg: str, context: str = "") -> bool:
        """發送錯誤通知"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        message = f"""
⚠️ <b>系統錯誤通知</b> ⚠️

⏰ <b>時間:</b> {timestamp}
🔍 <b>錯誤內容:</b> {error_msg}
📋 <b>錯誤位置:</b> {context}

請立即檢查系統狀態！
"""
        
        return self.send_message(message)
    
    def send_daily_summary(self, summary_data: Dict[str, Any]) -> bool:
        """發送每日總結"""
        timestamp = datetime.now().strftime('%Y-%m-%d')
        
        message = f"""
📊 <b>每日套利總結</b> 📊

📅 <b>日期:</b> {timestamp}

💰 <b>交易統計:</b>
• 總交易次數: {summary_data.get('total_trades', 0)}
• 成功交易: {summary_data.get('successful_trades', 0)}
• 失敗交易: {summary_data.get('failed_trades', 0)}

💵 <b>收益統計:</b>
• 總收益: {summary_data.get('total_pnl', 0):.2f}
• 平均收益: {summary_data.get('avg_pnl', 0):.2f}
• 最大收益: {summary_data.get('max_pnl', 0):.2f}
• 最大虧損: {summary_data.get('max_loss', 0):.2f}

📈 <b>價差統計:</b>
• 平均價差: {summary_data.get('avg_spread', 0):.3f}%
• 最大價差: {summary_data.get('max_spread', 0):.3f}%
• 最小價差: {summary_data.get('min_spread', 0):.3f}%
"""
        
        return self.send_message(message)
    
    def test_connection(self) -> bool:
        """測試 Telegram 連接"""
        print("🔍 測試 Telegram 連接...")
        
        if not self.bot_token or not self.chat_id:
            print("❌ Telegram 設定不完整")
            print("請在 config.env 中設定:")
            print("TELEGRAM_BOT_TOKEN=您的機器人Token")
            print("TELEGRAM_CHAT_ID=您的聊天ID")
            return False
        
        test_message = "🤖 XAU 套利系統連接測試成功！"
        if self.send_message(test_message):
            print("✅ Telegram 連接測試成功")
            return True
        else:
            print("❌ Telegram 連接測試失敗")
            return False

# 測試函數
if __name__ == "__main__":
    notifier = TelegramNotifier()
    notifier.test_connection() 