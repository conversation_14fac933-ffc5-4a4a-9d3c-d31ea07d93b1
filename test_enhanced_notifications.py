#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的通知功能
"""

import os
import sys
from datetime import datetime, timedelta

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from telegram_notifier import TelegramNotifier
from daily_report_generator import DailyReportGenerator

def test_enhanced_trade_close_notification():
    """测试增强的平仓通知"""
    print("🧪 测试增强的平仓通知...")
    
    notifier = TelegramNotifier()
    
    # 模拟详细的平仓信息
    close_info = {
        'trade_id': 'TEST_ENHANCED_001',
        'bybit_open_price': 3300.0,
        'bybit_close_price': 3305.0,
        'bybit_qty': 0.1,
        'bybit_pnl': 0.5,
        'bybit_total_fee': 0.066,
        
        'mt5_open_price': 3315.0,
        'mt5_close_price': 3310.0,
        'mt5_qty': 0.10,
        'mt5_pnl': -0.5,
        'mt5_total_fee': 0.033,
        
        'total_fees': 0.099,
        'gross_profit': 15.0,
        'net_profit': 14.901,
        'roi': 2.25,
        'duration': '00:05:30',
        
        'bybit_balance_after': 28.95,
        'mt5_balance_after': 30.65,
        'total_value_usd': 59.60,
        
        'daily_trades': 3,
        'daily_profit': 42.50,
        'success_rate': 85.7
    }
    
    success = notifier.send_trade_close(close_info)
    
    if success:
        print("✅ 增强平仓通知发送成功")
    else:
        print("❌ 增强平仓通知发送失败")
    
    return success

def test_daily_report():
    """测试每日报告功能"""
    print("🧪 测试每日报告功能...")
    
    generator = DailyReportGenerator()
    
    # 生成测试报告（昨天的日期）
    yesterday = datetime.now() - timedelta(days=1)
    test_date = yesterday.strftime('%Y-%m-%d')
    
    success = generator.generate_and_send_report(test_date)
    
    if success:
        print("✅ 每日报告生成并发送成功")
    else:
        print("✅ 每日报告功能正常（无交易数据时的正常行为）")
    
    return True

def test_sample_daily_report():
    """测试示例每日报告"""
    print("🧪 测试示例每日报告...")
    
    notifier = TelegramNotifier()
    
    # 模拟每日报告数据
    sample_report = {
        'date': '2025-01-07',
        'total_trades': 8,
        'successful_trades': 7,
        'failed_trades': 1,
        'success_rate': 87.5,
        
        'gross_profit': 125.50,
        'total_fees': 2.45,
        'net_profit': 123.05,
        'avg_profit_per_trade': 15.38,
        'max_profit': 28.50,
        'min_profit': -5.20,
        
        'avg_duration': '00:08:45',
        'max_duration': '00:15:30',
        'min_duration': '00:03:20',
        
        'bybit_balance_start': 28.50,
        'bybit_balance_end': 29.15,
        'mt5_balance_start': 30.20,
        'mt5_balance_end': 30.85,
        'total_change': 1.30,
        
        'avg_entry_spread': 0.345,
        'avg_exit_spread': 0.125,
        'max_spread': 0.520,
        
        'risk_triggers': 0,
        'max_drawdown': 0.0,
        'active_positions': 2,
        
        'trade_details': """1. ID: TEST001...
   時間: 2025-01-07 09:15 - 2025-01-07 09:23
   收益: 18.50 USD
   價差: 0.350% → 0.120%

2. ID: TEST002...
   時間: 2025-01-07 10:30 - 2025-01-07 10:38
   收益: 22.30 USD
   價差: 0.380% → 0.110%

3. ID: TEST003...
   時間: 2025-01-07 14:45 - 2025-01-07 15:00
   收益: 28.50 USD
   價差: 0.420% → 0.095%"""
    }
    
    success = notifier.send_daily_report(sample_report)
    
    if success:
        print("✅ 示例每日报告发送成功")
    else:
        print("❌ 示例每日报告发送失败")
    
    return success

def main():
    """主测试函数"""
    print("🚀 增强通知功能测试")
    print("=" * 50)
    
    results = []
    
    # 测试各种增强功能
    results.append(("增强平仓通知", test_enhanced_trade_close_notification()))
    results.append(("每日报告功能", test_daily_report()))
    results.append(("示例每日报告", test_sample_daily_report()))
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:<15}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有增强功能测试通过！")
    else:
        print("⚠️ 部分增强功能测试失败")

if __name__ == "__main__":
    main()
