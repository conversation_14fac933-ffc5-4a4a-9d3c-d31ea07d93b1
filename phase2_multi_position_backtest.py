"""
BYBIT XAU套利回測系統 - 多倉位版本
支持同時持有多個倉位，每分鐘檢測進場條件
"""
import pandas as pd
import numpy as np
import json
import os
from datetime import datetime

class XAUArbitrageMultiBacktest:
    def __init__(self, initial_capital=1000, leverage=10, position_size_pct=0.01):
        """初始化回測系統"""
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.leverage = leverage
        self.position_size_pct = position_size_pct

        self.trades = []
        self.open_positions = []  # 多個同時持倉
        self.total_pnl = 0
        self.win_trades = 0
        self.lose_trades = 0

        # 手續費設置
        self.bybit_fee_rate = 0.0006  # BYBIT永續合約手續費 0.06%
        self.mt5_fee_rate = 0.0003    # MT5外匯手續費約 0.03%
        
    def load_data(self):
        """載入歷史數據"""
        try:
            data = pd.read_csv('data/xau_spread_analysis_simple.csv')
            data['time'] = pd.to_datetime(data['time'])
            data = data.sort_values('time').reset_index(drop=True)
            print(f"✅ 成功載入 {len(data)} 筆歷史數據")
            return data
        except FileNotFoundError:
            print("❌ 找不到數據文件: data/xau_spread_analysis_simple.csv")
            return None
    
    def backtest_user_strategy(self, data):
        """用戶策略回測 - 多倉位版本"""
        entry_threshold = -0.85  # 進場閾值
        take_profit = -0.1       # 止盈
        stop_loss = 2.0          # 止損
        
        print(f"🔄 開始回測用戶策略（多倉位版本）")
        print(f"進場閾值: {entry_threshold}% | 止盈: {take_profit}% | 止損: +{stop_loss}%")
        print(f"槓桿: {self.leverage}x | 初始本金: ${self.initial_capital} | 每筆交易: {self.position_size_pct*100}%本金")
        print(f"BYBIT手續費: {self.bybit_fee_rate*100:.3f}% | MT5手續費: {self.mt5_fee_rate*100:.3f}%")
        print("-" * 80)
        
        trade_count = 0
        for i, row in data.iterrows():
            current_spread = row['price_diff_pct']
            current_time = row['time']
            bybit_price = row['bybit_price']
            mt5_price = row['mt5_price']
            
            # 檢查進場條件 - 每分鐘都檢查，符合條件就開新倉
            if current_spread <= entry_threshold:  # 負差價達到-0.85%進場
                trade_count += 1
                # 計算當前交易金額（1%本金）
                trade_amount = self.current_capital * self.position_size_pct
                
                new_position = {
                    'position_id': trade_count,
                    'entry_time': current_time,
                    'entry_spread': current_spread,
                    'entry_bybit_price': bybit_price,
                    'entry_mt5_price': mt5_price,
                    'trade_amount': trade_amount,
                    'capital_at_entry': self.current_capital,
                    'strategy': 'short_spread'  # 做空差價（買Bybit，賣MT5）
                }
                self.open_positions.append(new_position)
                print(f"📈 進場#{trade_count} | 時間: {current_time} | 差價: {current_spread:.4f}% | 交易金額: ${trade_amount:.2f} | 持倉數: {len(self.open_positions)}")
            
            # 檢查所有持倉的出場條件
            positions_to_close = []
            for pos_idx, position in enumerate(self.open_positions):
                # 止盈條件：差價收窄到-0.1%
                if current_spread >= take_profit:
                    positions_to_close.append((pos_idx, position, 'take_profit'))
                
                # 止損條件：差價擴大到+2%
                elif current_spread >= stop_loss:
                    positions_to_close.append((pos_idx, position, 'stop_loss'))
            
            # 執行平倉（從後往前關閉，避免索引問題）
            for pos_idx, position, exit_reason in reversed(positions_to_close):
                self._close_position(row, position, exit_reason)
                self.open_positions.pop(pos_idx)
        
        # 強制平倉所有剩餘持倉
        if self.open_positions:
            print(f"\n🔚 強制平倉剩餘 {len(self.open_positions)} 個持倉")
            for position in self.open_positions:
                self._close_position(data.iloc[-1], position, 'force_close')
            self.open_positions.clear()

        # 計算回測結果
        return self._calculate_results()
    
    def _close_position(self, row, position, exit_reason):
        """平倉並計算盈虧"""
        exit_time = row['time']
        exit_spread = row['price_diff_pct']
        
        # 計算差價變化
        entry_spread = position['entry_spread']
        
        # 套利邏輯：進場-0.85%，出場-0.1%，收益 = 0.85% - 0.1% = 0.75%
        if entry_spread < 0:  # 負差價進場（Bybit < MT5）
            base_return_pct = abs(entry_spread) - abs(exit_spread)
        else:  # 正差價進場（Bybit > MT5）
            base_return_pct = abs(entry_spread) - abs(exit_spread)
        
        # 應用槓桿
        leveraged_return_pct = base_return_pct * self.leverage
        
        # 計算手續費（雙邊）
        fee_cost_pct = (self.bybit_fee_rate + self.mt5_fee_rate) * 2
        
        # 淨收益率
        net_return_pct = leveraged_return_pct - fee_cost_pct
        
        # 計算實際盈虧
        trade_amount = position['trade_amount']
        trade_pnl = trade_amount * (net_return_pct / 100)
        
        # 更新資本
        self.current_capital += trade_pnl
        self.total_pnl += trade_pnl
        
        # 記錄交易
        duration_minutes = (pd.to_datetime(exit_time) - pd.to_datetime(position['entry_time'])).total_seconds() / 60
        
        trade_record = {
            'position_id': position['position_id'],
            'entry_time': position['entry_time'],
            'exit_time': exit_time,
            'entry_spread': entry_spread,
            'exit_spread': exit_spread,
            'spread_narrowing': base_return_pct,
            'base_return_pct': base_return_pct,
            'leveraged_return_pct': leveraged_return_pct,
            'fee_cost_pct': fee_cost_pct,
            'net_return_pct': net_return_pct,
            'trade_amount': trade_amount,
            'pnl': trade_pnl,
            'capital_before': position['capital_at_entry'],
            'capital_after': self.current_capital,
            'exit_reason': exit_reason,
            'duration_minutes': duration_minutes
        }
        
        self.trades.append(trade_record)
        
        if trade_pnl > 0:
            self.win_trades += 1
            result_icon = "✅"
        else:
            self.lose_trades += 1
            result_icon = "❌"
        
        remaining_positions = len(self.open_positions) - 1 if exit_reason != 'force_close' else 0
        print(f"{result_icon} 出場#{position['position_id']} | 時間: {exit_time} | 差價: {exit_spread:.4f}% | "
              f"原因: {exit_reason} | 差價收窄: {base_return_pct:.4f}% | "
              f"槓桿收益: {leveraged_return_pct:.2f}% | 淨收益: {net_return_pct:.2f}% | "
              f"盈虧: ${trade_pnl:.2f} | 資本: ${self.current_capital:.2f} | "
              f"持倉: {duration_minutes:.0f}分鐘 | 剩餘持倉: {remaining_positions}")
    
    def _calculate_results(self):
        """計算回測結果"""
        if not self.trades:
            return {
                'initial_capital': self.initial_capital,
                'final_capital': self.current_capital,
                'total_trades': 0,
                'win_rate': 0,
                'total_pnl': 0,
                'total_return_pct': 0
            }
        
        trades_df = pd.DataFrame(self.trades)
        
        total_trades = len(self.trades)
        win_rate = (self.win_trades / total_trades) * 100 if total_trades > 0 else 0
        total_return_pct = ((self.current_capital - self.initial_capital) / self.initial_capital) * 100
        avg_pnl_per_trade = self.total_pnl / total_trades if total_trades > 0 else 0
        max_profit = trades_df['pnl'].max() if not trades_df.empty else 0
        max_loss = trades_df['pnl'].min() if not trades_df.empty else 0
        avg_duration = trades_df['duration_minutes'].mean() if not trades_df.empty else 0
        
        exit_reasons = trades_df['exit_reason'].value_counts().to_dict() if not trades_df.empty else {}
        
        return {
            'initial_capital': self.initial_capital,
            'final_capital': self.current_capital,
            'total_trades': total_trades,
            'win_trades': self.win_trades,
            'lose_trades': self.lose_trades,
            'win_rate': win_rate,
            'total_pnl': self.total_pnl,
            'total_return_pct': total_return_pct,
            'avg_pnl_per_trade': avg_pnl_per_trade,
            'max_profit': max_profit,
            'max_loss': max_loss,
            'avg_duration': avg_duration,
            'exit_reasons': exit_reasons,
            'trades': self.trades
        }
    
    def run_user_strategy(self):
        """運行用戶策略"""
        data = self.load_data()
        if data is None:
            return
        
        print("="*100)
        print("BYBIT XAU套利策略回測結果 - 多倉位版本")
        print("="*100)
        
        result = self.backtest_user_strategy(data)
        self._generate_report(result)
        self._save_results(result)
        
        return result
    
    def _generate_report(self, result):
        """生成報告"""
        print("\n" + "="*100)
        print("多倉位策略回測報告")
        print("="*100)
        
        print(f"📊 總體表現:")
        print(f"   初始資本: ${result['initial_capital']:.2f}")
        print(f"   最終資本: ${result['final_capital']:.2f}")
        print(f"   總盈虧: ${result['total_pnl']:.2f}")
        print(f"   總收益率: {result['total_return_pct']:.2f}%")
        
        print(f"\n📈 交易統計:")
        print(f"   總交易次數: {result['total_trades']}")
        print(f"   獲利交易: {result['win_trades']}")
        print(f"   虧損交易: {result['lose_trades']}")
        print(f"   勝率: {result['win_rate']:.1f}%")
        print(f"   平均每筆盈虧: ${result['avg_pnl_per_trade']:.2f}")
        print(f"   最大單筆盈利: ${result['max_profit']:.2f}")
        print(f"   最大單筆虧損: ${result['max_loss']:.2f}")
        print(f"   平均持倉時間: {result['avg_duration']:.0f}分鐘")
        
        if 'exit_reasons' in result and result['exit_reasons']:
            print(f"\n📋 出場原因統計:")
            for reason, count in result['exit_reasons'].items():
                print(f"   {reason}: {count}次")
    
    def _save_results(self, result):
        """保存結果"""
        os.makedirs('data', exist_ok=True)
        
        with open('data/multi_position_results.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        
        if result['trades']:
            trades_df = pd.DataFrame(result['trades'])
            trades_df.to_csv('data/multi_position_trades.csv', index=False)
        
        print(f"\n✅ 多倉位策略結果已保存:")
        print(f"   - data/multi_position_results.json")
        if result['trades']:
            print(f"   - data/multi_position_trades.csv")

def main():
    """主函數"""
    backtest = XAUArbitrageMultiBacktest(
        initial_capital=1000,  # $1000初始資本
        leverage=10,           # 10倍槓桿
        position_size_pct=0.01 # 每筆交易1%本金
    )
    backtest.run_user_strategy()

if __name__ == "__main__":
    main()
