#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from dotenv import load_dotenv

# 載入環境變數
load_dotenv('config.env')

def test_bybit_api():
    """測試 Bybit API 連接和下單功能"""
    
    # 檢查環境變數
    api_key = os.getenv('BYBIT_API_KEY')
    api_secret = os.getenv('BYBIT_API_SECRET')
    
    print("=== Bybit API 測試 ===")
    print(f"API Key: {api_key}")
    print(f"API Secret: {api_secret[:10]}..." if api_secret else "API Secret: None")
    print()
    
    if not api_key or not api_secret:
        print("❌ API Key 或 Secret 未設定")
        return
    
    try:
        from bybit_futures_client import BybitFuturesClient
        
        # 創建客戶端
        client = BybitFuturesClient()
        
        # 測試餘額查詢
        print("1. 測試餘額查詢...")
        balance_result = client.get_account_info()
        if balance_result:
            print("✅ 餘額查詢成功")
            wallet_list = balance_result.get("list", [])
            if wallet_list:
                wallet = wallet_list[0]
                total_balance = float(wallet.get("totalWalletBalance", 0))
                print(f"   總餘額: {total_balance:.2f} USDT")
        else:
            print("❌ 餘額查詢失敗")
        print()
        
        # 測試下單
        print("2. 測試下單功能...")
        order_result = client.place_order('XAUTUSDT', 'Buy', 0.001, 'Market')
        if order_result:
            print("✅ 下單成功！")
            print(f"   訂單ID: {order_result.get('orderId', 'N/A')}")
        else:
            print("❌ 下單失敗")
            print("   這表示API權限不足或簽名錯誤")
        print()
        
        # 測試連接
        print("3. 測試完整連接...")
        client.test_connection()
            
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    test_bybit_api() 