#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Bybit 下單簽名修復
"""

import os
import sys
from dotenv import load_dotenv
from bybit_futures_client import BybitFuturesClient

load_dotenv('config.env')

def test_bybit_order():
    """測試 Bybit 下單"""
    print("🔍 測試 Bybit 下單簽名修復...")
    
    # 初始化客戶端
    client = BybitFuturesClient()
    
    # 測試連接
    print("📡 測試 Bybit 連接...")
    account_info = client.get_account_info()
    if not account_info:
        print("❌ Bybit 連接失敗")
        return False
    
    print("✅ Bybit 連接成功")
    
    # 獲取帳戶餘額
    wallet_list = account_info.get("list", [])
    if wallet_list:
        wallet = wallet_list[0]
        available_balance = float(wallet.get("totalAvailableBalance", 0))
        print(f"💰 可用餘額: {available_balance:.2f} USDT")
    
    # 測試下單（滿足最小訂單價值要求）
    print("📊 測試 Bybit 下單...")
    # 計算滿足5USDT最小訂單價值的數量
    current_price = client.get_ticker_price("XAUTUSDT")
    if current_price:
        min_qty = 5.0 / current_price  # 5USDT / 當前價格
        min_qty = round(min_qty, 3)  # 保留3位小數
        print(f"💰 當前價格: {current_price:.2f} USDT")
        print(f"📊 最小訂單數量: {min_qty} (滿足5USDT要求)")
    else:
        min_qty = 0.002  # 預設值
    
    result = client.place_order(
        symbol="XAUTUSDT",
        side="Buy",
        qty=min_qty,
        order_type="Market",
        time_in_force="GTC"
    )
    
    if result:
        print("✅ Bybit 下單成功！")
        print(f"   訂單號: {result.get('orderId', 'N/A')}")
        return True
    else:
        print("❌ Bybit 下單失敗")
        return False

if __name__ == "__main__":
    success = test_bybit_order()
    if success:
        print("🎉 Bybit 下單測試通過！")
    else:
        print("💥 Bybit 下單測試失敗！")
        sys.exit(1) 