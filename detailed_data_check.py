"""
詳細數據檢查 - 重點檢查時間間隔和數據異常
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def detailed_data_analysis():
    """詳細分析數據"""
    
    # 載入數據
    data = pd.read_csv('data/xau_spread_analysis_simple.csv')
    data['time'] = pd.to_datetime(data['time'])
    data = data.sort_values('time').reset_index(drop=True)
    
    print(f"📊 詳細數據分析")
    print("=" * 80)
    
    # 檢查時間間隔異常
    data['time_diff_minutes'] = data['time'].diff().dt.total_seconds() / 60
    
    print(f"\n⏰ 時間間隔分析:")
    print(f"   平均間隔: {data['time_diff_minutes'].mean():.2f} 分鐘")
    print(f"   中位數間隔: {data['time_diff_minutes'].median():.2f} 分鐘")
    print(f"   最小間隔: {data['time_diff_minutes'].min():.2f} 分鐘")
    print(f"   最大間隔: {data['time_diff_minutes'].max():.2f} 分鐘")
    
    # 找出異常的時間間隔
    large_gaps = data[data['time_diff_minutes'] > 60]  # 超過1小時的間隔
    if len(large_gaps) > 0:
        print(f"\n⚠️ 發現 {len(large_gaps)} 個超過1小時的時間間隔:")
        for i, row in large_gaps.head(10).iterrows():
            prev_time = data.iloc[i-1]['time'] if i > 0 else None
            print(f"   {prev_time} -> {row['time']} (間隔: {row['time_diff_minutes']:.0f}分鐘)")
    
    # 檢查每天的數據分佈
    print(f"\n📅 每日數據分佈詳情:")
    data['date'] = data['time'].dt.date
    data['hour'] = data['time'].dt.hour
    
    daily_stats = data.groupby('date').agg({
        'time': ['count', 'min', 'max'],
        'price_diff_pct': ['min', 'max', 'mean']
    }).round(4)
    
    daily_stats.columns = ['數據點數', '開始時間', '結束時間', '最小差價%', '最大差價%', '平均差價%']
    print(daily_stats)
    
    # 檢查-0.85%數據點的時間分佈
    extreme_data = data[data['price_diff_pct'] <= -0.85]
    
    print(f"\n🔍 -0.85%數據點的詳細分析:")
    print(f"   總數: {len(extreme_data)} 個")
    
    if len(extreme_data) > 0:
        extreme_daily = extreme_data.groupby('date').size()
        print(f"\n   每日分佈:")
        for date, count in extreme_daily.items():
            print(f"     {date}: {count}個")
        
        print(f"\n   時間分佈 (前20個):")
        print("     日期時間 | BYBIT | MT5 | 差價% | 前一筆間隔(分)")
        print("-" * 70)
        
        for i, row in extreme_data.head(20).iterrows():
            prev_time_diff = row['time_diff_minutes'] if not pd.isna(row['time_diff_minutes']) else 0
            print(f"     {row['time']} | {row['bybit_price']:7.2f} | {row['mt5_price']:7.2f} | {row['price_diff_pct']:6.3f}% | {prev_time_diff:6.0f}")
    
    # 檢查是否有數據缺失的時段
    print(f"\n🕐 檢查數據連續性:")
    
    # 檢查每小時的數據點數
    hourly_counts = data.groupby([data['time'].dt.date, data['time'].dt.hour]).size()
    
    # 找出數據點數異常少的小時
    low_data_hours = hourly_counts[hourly_counts < 10]  # 每小時少於10個數據點
    
    if len(low_data_hours) > 0:
        print(f"   發現 {len(low_data_hours)} 個小時數據點少於10個:")
        for (date, hour), count in low_data_hours.head(10).items():
            print(f"     {date} {hour:02d}:00 - 只有 {count} 個數據點")
    
    # 檢查價格是否有異常跳躍
    print(f"\n💰 價格跳躍分析:")
    
    data['bybit_change'] = data['bybit_price'].diff().abs()
    data['mt5_change'] = data['mt5_price'].diff().abs()
    
    large_bybit_jumps = data[data['bybit_change'] > 20]  # BYBIT價格跳躍超過20美元
    large_mt5_jumps = data[data['mt5_change'] > 20]      # MT5價格跳躍超過20美元
    
    if len(large_bybit_jumps) > 0:
        print(f"   BYBIT大幅跳躍 ({len(large_bybit_jumps)}個):")
        for i, row in large_bybit_jumps.head(5).iterrows():
            prev_price = data.iloc[i-1]['bybit_price'] if i > 0 else 0
            time_gap = row['time_diff_minutes'] if not pd.isna(row['time_diff_minutes']) else 0
            print(f"     {row['time']}: {prev_price:.2f} -> {row['bybit_price']:.2f} (變化:{row['bybit_change']:.2f}, 間隔:{time_gap:.0f}分)")
    
    if len(large_mt5_jumps) > 0:
        print(f"   MT5大幅跳躍 ({len(large_mt5_jumps)}個):")
        for i, row in large_mt5_jumps.head(5).iterrows():
            prev_price = data.iloc[i-1]['mt5_price'] if i > 0 else 0
            time_gap = row['time_diff_minutes'] if not pd.isna(row['time_diff_minutes']) else 0
            print(f"     {row['time']}: {prev_price:.2f} -> {row['mt5_price']:.2f} (變化:{row['mt5_change']:.2f}, 間隔:{time_gap:.0f}分)")
    
    # 檢查極端差價是否與價格跳躍相關
    print(f"\n🔗 極端差價與價格跳躍的關聯:")

    # 為極端數據添加價格變化列
    extreme_data = extreme_data.copy()
    extreme_data['bybit_change'] = data.loc[extreme_data.index, 'bybit_change']
    extreme_data['mt5_change'] = data.loc[extreme_data.index, 'mt5_change']

    extreme_with_jumps = extreme_data[
        (extreme_data['bybit_change'] > 10) |
        (extreme_data['mt5_change'] > 10) |
        (extreme_data['time_diff_minutes'] > 30)
    ]
    
    if len(extreme_with_jumps) > 0:
        print(f"   {len(extreme_with_jumps)} 個極端差價點伴隨價格跳躍或時間間隔異常:")
        for i, row in extreme_with_jumps.head(10).iterrows():
            print(f"     {row['time']}: 差價{row['price_diff_pct']:.3f}%, "
                  f"BYBIT變化{row['bybit_change']:.1f}, MT5變化{row['mt5_change']:.1f}, "
                  f"時間間隔{row['time_diff_minutes']:.0f}分")
    else:
        print("   ✅ 極端差價點沒有明顯的價格跳躍或時間異常")
    
    # 檢查數據是否在交易時間內
    print(f"\n🕒 交易時間分析:")
    data['weekday'] = data['time'].dt.dayofweek  # 0=Monday, 6=Sunday
    data['hour'] = data['time'].dt.hour
    
    # 檢查是否有週末數據
    weekend_data = data[data['weekday'].isin([5, 6])]  # Saturday, Sunday
    if len(weekend_data) > 0:
        print(f"   ⚠️ 發現 {len(weekend_data)} 個週末數據點")
        weekend_extreme = weekend_data[weekend_data['price_diff_pct'] <= -0.85]
        if len(weekend_extreme) > 0:
            print(f"     其中 {len(weekend_extreme)} 個是極端差價點")
    
    # 檢查夜間數據
    night_data = data[(data['hour'] < 6) | (data['hour'] > 22)]
    if len(night_data) > 0:
        night_extreme = night_data[night_data['price_diff_pct'] <= -0.85]
        print(f"   夜間數據 (22:00-06:00): {len(night_data)} 個，其中極端差價 {len(night_extreme)} 個")
    
    # 最終結論
    print(f"\n📋 數據質量評估:")
    print(f"   1. 時間跨度: {(data['time'].max() - data['time'].min()).days + 1} 天")
    print(f"   2. 數據密度: 平均每分鐘 {len(data) / ((data['time'].max() - data['time'].min()).total_seconds() / 60):.3f} 個點")
    print(f"   3. 極端差價點: {len(extreme_data)} 個 ({len(extreme_data)/len(data)*100:.2f}%)")
    print(f"   4. 最大時間間隔: {data['time_diff_minutes'].max():.0f} 分鐘")
    
    if data['time_diff_minutes'].max() > 1440:  # 超過24小時
        print("   ⚠️ 警告: 存在超過24小時的數據間隔，可能影響分析準確性")
    
    if len(extreme_with_jumps) > len(extreme_data) * 0.5:
        print("   ⚠️ 警告: 超過50%的極端差價點伴隨異常，數據可能不可靠")
    
    return data, extreme_data

def main():
    detailed_data_analysis()

if __name__ == "__main__":
    main()
