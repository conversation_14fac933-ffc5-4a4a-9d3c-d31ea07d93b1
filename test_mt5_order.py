#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from dotenv import load_dotenv
from bybit_mt5_client import BybitMT5Client
import time

load_dotenv('config.env')

def test_mt5_order():
    """測試 MT5 下單功能"""
    print("=== 測試 MT5 下單功能 ===")
    
    # 初始化 MT5 客戶端
    mt5_client = BybitMT5Client()
    
    # 測試1: 連接 MT5
    print("\n1. 測試 MT5 連接...")
    if mt5_client.connect():
        print("✅ MT5 連接成功")
        print(f"   伺服器: {mt5_client.server}")
        print(f"   帳號: {mt5_client.login}")
    else:
        print("❌ MT5 連接失敗")
        return
    
    # 測試2: 獲取帳戶資訊
    print("\n2. 測試獲取帳戶資訊...")
    account_info = mt5_client.get_account_info()
    if account_info:
        print("✅ 帳戶資訊獲取成功")
        print(f"   餘額: {account_info.get('balance', 'N/A')}")
        print(f"   權益: {account_info.get('equity', 'N/A')}")
        print(f"   保證金: {account_info.get('margin', 'N/A')}")
    else:
        print("❌ 帳戶資訊獲取失敗")
    
    # 測試3: 獲取 XAUUSD+ 價格
    print("\n3. 測試獲取 XAUUSD+ 價格...")
    tick = mt5_client.get_tick("XAUUSD+")
    print(f"   獲取到的 tick: {tick}")
    if tick:
        print("✅ 價格獲取成功")
        print(f"   買價: {tick.get('ask', 'N/A')}")
        print(f"   賣價: {tick.get('bid', 'N/A')}")
        try:
            current_price = tick.get('ask', 0)
            print(f"   設置 current_price: {current_price}")
        except Exception as e:
            print(f"   設置 current_price 錯誤: {e}")
            current_price = 0
    else:
        print("❌ 價格獲取失敗")
        print("   嘗試獲取其他 XAU 交易對...")
        # 嘗試其他可能的 XAU 交易對
        for symbol in ["XAUUSD", "XAUUSD.m", "XAUUSD+"]:
            print(f"   嘗試 {symbol}...")
            tick = mt5_client.get_tick(symbol)
            if tick:
                print(f"✅ 找到 {symbol} 價格")
                print(f"   買價: {tick.get('ask', 'N/A')}")
                print(f"   賣價: {tick.get('bid', 'N/A')}")
                current_price = tick.get('ask', 0)
                break
        else:
            print("❌ 所有 XAU 交易對都無法獲取價格")
            mt5_client.disconnect()
            return
    
    print(f"   繼續執行，current_price: {current_price}")
    
    # 測試4: 下測試單（買入 0.01 手）
    print("\n4. 測試下單功能...")
    try:
        test_volume = 0.01  # 最小下單量
        # 根據 order_type 自動帶入價格
        order_type = 0  # 0=Buy, 1=Sell
        if order_type == 0:
            test_price = current_price
        else:
            test_price = current_price
        test_order = mt5_client.place_order(
            symbol="XAUUSD+",
            order_type=order_type,  # 0=Buy, 1=Sell
            volume=test_volume,
            price=test_price,  # 市價單傳入對應價格
            comment="Test Order"
        )
        
        if test_order:
            print("✅ MT5 下單成功")
            print(f"   訂單號: {test_order.get('order', 'N/A')}")
            print(f"   下單數量: {test_volume}")
            print(f"   下單價格: {test_price}")
            
            # 等待一下讓訂單執行
            import time
            time.sleep(2)
            
            # 測試5: 查詢持倉
            print("\n5. 測試查詢持倉...")
            positions = mt5_client.get_positions("XAUUSD+")
            if positions:
                print("✅ 持倉查詢成功")
                for pos in positions:
                    print(f"   持倉號: {pos['ticket']}, 數量: {pos['volume']}, 類型: {pos['type']}")
                # 測試6: 平倉
                print("\n6. 測試平倉...")
                for pos in positions:
                    close_result = mt5_client.close_position(pos['ticket'])
                    if close_result:
                        print(f"✅ 平倉成功: 持倉號 {pos['ticket']}")
                    else:
                        print(f"❌ 平倉失敗: 持倉號 {pos['ticket']}")
            else:
                print("❌ 沒有查到持倉，無法平倉")
        else:
            print("❌ MT5 下單失敗，回傳為 None 或空。請檢查 MT5 日誌與參數。")
    except Exception as e:
        print(f"❌ 測試下單異常: {e}")
    
    # 斷開連接
    mt5_client.disconnect()
    print("\n🔌 MT5 連接已斷開")
    print("=== 測試完成 ===")

if __name__ == "__main__":
    test_mt5_order() 