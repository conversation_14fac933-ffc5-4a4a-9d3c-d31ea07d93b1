# MT5 自動交易開啟指南

## 問題
你的 MT5 下單一直出現：`retcode=10027, comment=AutoTrading disabled by client`

## 解決方法

### 方法一：主工具列按鈕（最重要！）

1. **打開 MT5 桌面版軟體**（不是網頁版！）
2. **看螢幕最上方主工具列**，找到一排按鈕：
   - 新訂單、新圖表、市場深度、等等...
3. **找到「自動交易」按鈕**：
   - 圖示：綠色三角形 ▶️ 或紅色圓圈 ⛔
   - 文字：可能寫「自動交易」或「AutoTrading」
4. **點擊讓它變成綠色！**
   - 綠色 = 允許自動交易 ✅
   - 紅色 = 禁止自動交易 ❌

### 方法二：選項設定

1. 在 MT5 軟體上方選單點選「工具」→「選項」
2. 切換到「EA交易」或「Expert Advisors」分頁
3. 勾選「允許自動交易」
4. 按「確定」儲存

### 方法三：檢查狀態

如果按鈕已經是綠色但還是無法下單：

1. **重新點擊按鈕**：先點紅色，再點綠色
2. **重啟 MT5 軟體**
3. **檢查是否有其他限制**

## 常見問題

### Q: 找不到自動交易按鈕？
A: 確保你用的是 MT5 桌面版，不是網頁版。按鈕在主工具列上。

### Q: 按鈕已經是綠色但還是無法下單？
A: 重新點擊按鈕，或重啟 MT5 軟體。

### Q: 按鈕是灰色的？
A: 可能是帳戶類型限制，聯繫你的券商。

## 測試確認

開啟自動交易後，執行測試腳本：
```bash
python test_complete_trading.py
```

應該會看到：
```
✅ MT5 下單成功！
   訂單號: xxxxx
```

而不是：
```
❌ MT5 下單失敗: AutoTrading disabled by client
``` 