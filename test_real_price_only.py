#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实价格获取（无模拟数据）
验证：
1. 只获取真实价格，失败就报错
2. 移除资金费率限制
3. 正确的DEBUG CODE输出
"""

import os
import sys
import time
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_bybit_spot_price():
    """测试真实Bybit现货价格获取"""
    print("=" * 60)
    print("🧪 测试真实Bybit现货XAUUSDT价格获取")
    print("=" * 60)
    
    try:
        import requests
        
        response = requests.get(
            'https://api.bybit.com/v5/market/tickers',
            params={'category': 'spot', 'symbol': 'XAUUSDT'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('retCode') == 0 and data.get('result', {}).get('list'):
                ticker = data['result']['list'][0]
                price = float(ticker['lastPrice'])
                
                print(f"✅ 真实Bybit现货XAUUSDT价格: ${price:.2f}")
                
                if price > 1000 and price < 5000:
                    print(f"✅ 价格在合理范围内")
                    return price
                else:
                    print(f"❌ DEBUG_CODE_003: 价格超出合理范围 {price}")
                    return None
            else:
                print(f"❌ DEBUG_CODE_004: API返回错误数据: {data}")
                return None
        else:
            print(f"❌ DEBUG_CODE_005: API请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ DEBUG_CODE_006: 获取价格异常: {e}")
        return None

def test_cloud_mt5_real_price():
    """测试云端MT5客户端真实价格获取"""
    print("=" * 60)
    print("🧪 测试云端MT5客户端真实价格获取")
    print("=" * 60)
    
    try:
        from bybit_mt5_client_cloud import BybitMT5ClientCloud
        
        client = BybitMT5ClientCloud()
        
        if client.connect():
            print("✅ 云端MT5客户端连接成功")
            
            # 测试价格获取
            price = client.get_current_price("XAUUSD+")
            
            if price:
                print(f"✅ 获取到真实价格: ${price:.2f}")
                client.disconnect()
                return price
            else:
                print("❌ 无法获取真实价格（应该有DEBUG_CODE输出）")
                client.disconnect()
                return None
        else:
            print("❌ 云端MT5客户端连接失败")
            return None
            
    except Exception as e:
        print(f"❌ 云端MT5客户端测试异常: {e}")
        return None

def test_arbitrage_conditions_no_funding_rate():
    """测试套利条件（不考虑资金费率）"""
    print("=" * 60)
    print("🧪 测试套利条件（不考虑资金费率）")
    print("=" * 60)
    
    try:
        from xau_arbitrage_trader import XAUArbitrageTrader
        
        trader = XAUArbitrageTrader()
        
        # 模拟不同的价差情况
        test_cases = [
            {'percentage': 0.25, 'expected': False, 'desc': '0.25% < 0.3%阈值'},
            {'percentage': 0.30, 'expected': True, 'desc': '0.30% = 0.3%阈值'},
            {'percentage': 0.35, 'expected': True, 'desc': '0.35% > 0.3%阈值'},
            {'percentage': -0.35, 'expected': True, 'desc': '-0.35% 绝对值 > 0.3%阈值'},
        ]
        
        all_passed = True
        
        for case in test_cases:
            spread_info = {
                'percentage': case['percentage'],
                'absolute': case['percentage'] * 3300 / 100,  # 假设价格3300
                'direction': 'MT5 > Bybit' if case['percentage'] > 0 else 'Bybit > MT5'
            }
            
            # 测试套利条件（资金费率设为任意值，应该不影响结果）
            funding_rate = 0.0001  # 正值资金费率
            result = trader.check_arbitrage_conditions(spread_info, funding_rate)
            
            status = "✅" if result == case['expected'] else "❌"
            print(f"{status} {case['desc']}: 预期{case['expected']}, 实际{result}")
            
            if result != case['expected']:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 套利条件测试异常: {e}")
        return False

def test_price_difference_calculation():
    """测试价差计算（使用真实价格）"""
    print("=" * 60)
    print("🧪 测试价差计算（使用真实价格）")
    print("=" * 60)
    
    try:
        from xau_arbitrage_trader import XAUArbitrageTrader
        
        trader = XAUArbitrageTrader()
        
        # 获取真实价格
        prices = trader.get_current_prices()
        
        if 'bybit' in prices and 'mt5' in prices:
            bybit_price = prices['bybit']
            mt5_price = prices['mt5']
            
            print(f"📊 真实价格:")
            print(f"   Bybit永续合约XAUTUSDT: ${bybit_price:.2f}")
            print(f"   MT5价格: ${mt5_price:.2f}")
            
            # 计算价差
            spread_info = trader.calculate_spread(bybit_price, mt5_price)
            
            print(f"📊 价差计算:")
            print(f"   绝对价差: ${spread_info['absolute']:.2f}")
            print(f"   百分比价差: {spread_info['percentage']:.4f}%")
            print(f"   价差方向: {spread_info['direction']}")
            
            # 检查价差是否合理
            abs_percentage = abs(spread_info['percentage'])
            if abs_percentage < 10.0:  # 价差应该小于10%
                print(f"✅ 价差在合理范围内 ({abs_percentage:.4f}% < 10%)")
                return True
            else:
                print(f"❌ 价差过大 ({abs_percentage:.4f}% >= 10%)")
                return False
        else:
            print("❌ 无法获取完整的真实价格数据")
            print(f"   获取到的价格: {list(prices.keys())}")
            return False
            
    except Exception as e:
        print(f"❌ 价差计算测试异常: {e}")
        return False

def test_funding_rate_display():
    """测试资金费率显示（仅供参考）"""
    print("=" * 60)
    print("🧪 测试资金费率显示（仅供参考）")
    print("=" * 60)
    
    try:
        from xau_arbitrage_trader import XAUArbitrageTrader
        
        trader = XAUArbitrageTrader()
        
        # 获取资金费率
        funding_rate = trader.bybit_client.get_funding_rate("XAUTUSDT")
        
        if funding_rate is not None:
            print(f"✅ 资金费率: {funding_rate:.6f} (仅供参考)")
            print("✅ 资金费率获取成功，但不影响交易决策")
            return True
        else:
            print("⚠️ 无法获取资金费率（不影响交易）")
            return True  # 这不是错误，因为资金费率不影响交易
            
    except Exception as e:
        print(f"❌ 资金费率测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始真实价格获取测试（无模拟数据）")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行各项测试
    tests = [
        ("真实Bybit现货价格", test_real_bybit_spot_price),
        ("云端MT5真实价格", test_cloud_mt5_real_price),
        ("套利条件（无资金费率限制）", test_arbitrage_conditions_no_funding_rate),
        ("价差计算（真实价格）", test_price_difference_calculation),
        ("资金费率显示", test_funding_rate_display),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = bool(result)
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 打印测试结果摘要
    print("\n" + "=" * 60)
    print("📊 真实价格获取测试结果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！真实价格获取系统正常")
        print("\n🔧 修复内容:")
        print("1. ✅ 只获取真实价格，失败就报错（带DEBUG_CODE）")
        print("2. ✅ 移除资金费率限制，只看价差阈值")
        print("3. ✅ 使用Bybit现货XAUUSDT作为MT5等效价格")
        print("4. ✅ 完整的错误处理和调试信息")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 测试完成，结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
