#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試交易執行功能
"""

import os
import sys
from dotenv import load_dotenv

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
load_dotenv('config.env')

from simulation_trader import XAUSimulationTrader

def test_trading_execution():
    """測試交易執行功能"""
    print("🧪 測試交易執行功能")
    print("=" * 60)
    
    # 創建模擬器並調整參數以便測試
    simulator = XAUSimulationTrader()
    
    # 🚨 調低閾值以便測試交易執行
    original_min_threshold = simulator.min_spread_threshold
    original_close_threshold = simulator.close_spread_threshold
    
    simulator.min_spread_threshold = 0.1  # 降低到0.1%
    simulator.close_spread_threshold = 0.02  # 降低到0.02%
    
    print(f"📊 測試參數調整:")
    print(f"   進場閾值: {original_min_threshold}% → {simulator.min_spread_threshold}%")
    print(f"   平倉閾值: {original_close_threshold}% → {simulator.close_spread_threshold}%")
    
    # 獲取當前價格
    prices = simulator.get_real_prices()
    if not prices:
        print("❌ 無法獲取價格，測試終止")
        return
    
    spread = simulator.calculate_spread(prices['bybit'], prices['mt5'])
    print(f"\n💰 當前市場狀況:")
    print(f"   Bybit: ${prices['bybit']:.2f}")
    print(f"   MT5: ${prices['mt5']:.2f}")
    print(f"   價差: {spread['percentage']:.3f}%")
    
    # 測試交易執行
    if abs(spread['percentage']) >= simulator.min_spread_threshold:
        print(f"\n🚨 價差達到測試閾值，執行模擬交易...")
        
        # 執行開倉
        trade_result = simulator.execute_arbitrage_trade(spread, prices)
        
        if trade_result:
            print(f"✅ 開倉成功: {trade_result['trade_id']}")
            print(f"   Bybit: {trade_result['bybit_order']['side']} {trade_result['bybit_order']['qty']} @ ${trade_result['bybit_order']['price']:.2f}")
            print(f"   MT5: {trade_result['mt5_order']['side']} {trade_result['mt5_order']['qty']} @ ${trade_result['mt5_order']['price']:.2f}")
            print(f"   手續費: ${trade_result['total_fees']:.2f}")
            
            # 更新績效
            performance = simulator.update_performance_metrics(prices)
            print(f"   當前淨值: ${performance['total_equity']:.2f}")
            
            # 測試平倉
            print(f"\n🔄 測試平倉功能...")
            close_result = simulator.close_arbitrage_trade(trade_result, prices)
            
            if close_result:
                print(f"✅ 平倉成功: {trade_result['trade_id']}")
                print(f"   Bybit盈虧: ${close_result['bybit_pnl']:+.2f}")
                print(f"   MT5盈虧: ${close_result['mt5_pnl']:+.2f}")
                print(f"   總盈虧: ${close_result['total_pnl']:+.2f}")
                print(f"   淨盈虧: ${close_result['net_pnl']:+.2f}")
                print(f"   持倉時間: {close_result['duration']}")
                
                # 最終績效
                final_performance = simulator.update_performance_metrics(prices)
                print(f"   最終淨值: ${final_performance['total_equity']:.2f}")
                
            else:
                print(f"❌ 平倉失敗")
        else:
            print(f"❌ 開倉失敗")
    else:
        print(f"\n⚠️ 當前價差 {spread['percentage']:.3f}% 未達測試閾值 {simulator.min_spread_threshold}%")
        print(f"💡 手動創建測試交易...")
        
        # 手動創建測試交易
        test_spread = {
            'percentage': 0.15,  # 模擬0.15%的價差
            'direction': 'MT5_higher',
            'absolute': prices['mt5'] - prices['bybit']
        }
        
        trade_result = simulator.execute_arbitrage_trade(test_spread, prices)
        
        if trade_result:
            print(f"✅ 測試交易創建成功: {trade_result['trade_id']}")
            
            # 立即測試平倉
            close_result = simulator.close_arbitrage_trade(trade_result, prices)
            if close_result:
                print(f"✅ 測試平倉成功，淨盈虧: ${close_result['net_pnl']:+.2f}")
            else:
                print(f"❌ 測試平倉失敗")
        else:
            print(f"❌ 測試交易創建失敗")
    
    # 恢復原始參數
    simulator.min_spread_threshold = original_min_threshold
    simulator.close_spread_threshold = original_close_threshold
    
    # 最終報告
    print(f"\n📊 測試完成報告:")
    final_summary = simulator.get_performance_summary()
    if final_summary:
        print(f"   總交易次數: {final_summary['total_trades']}")
        print(f"   活躍倉位: {final_summary['active_trades']}")
        print(f"   最終淨值: ${final_summary['current_equity']:,.2f}")
        print(f"   總收益: ${final_summary['total_return_usd']:+,.2f}")
        
        # 檢查帳戶狀態
        bybit_info = final_summary['bybit_account']
        mt5_info = final_summary['mt5_account']
        
        print(f"\n   帳戶狀態:")
        print(f"   Bybit - 餘額: ${bybit_info['balance']:,.2f}, 淨值: ${bybit_info['equity']:,.2f}")
        print(f"   MT5 - 餘額: ${mt5_info['balance']:,.2f}, 淨值: ${mt5_info['equity']:,.2f}")
    
    print(f"\n✅ 交易執行測試完成！")

if __name__ == "__main__":
    test_trading_execution()
