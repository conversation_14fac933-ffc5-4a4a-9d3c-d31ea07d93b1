"""
調試交易邏輯 - 檢查為什麼只有4次交易而不是61次
"""
import pandas as pd
import numpy as np

def debug_trading_logic():
    """調試交易邏輯"""
    
    # 載入數據
    try:
        data = pd.read_csv('data/xau_spread_analysis_simple.csv')
        data['time'] = pd.to_datetime(data['time'])
        data = data.sort_values('time').reset_index(drop=True)
        print(f"✅ 成功載入 {len(data)} 筆數據")
    except FileNotFoundError:
        print("❌ 找不到數據文件")
        return
    
    entry_threshold = -0.85
    take_profit = -0.1
    stop_loss = 2.0
    
    # 模擬交易邏輯
    current_position = None
    trades = []
    
    print(f"\n🔍 模擬交易過程:")
    print(f"進場條件: <= {entry_threshold}%")
    print(f"止盈條件: >= {take_profit}%") 
    print(f"止損條件: >= {stop_loss}%")
    print("-" * 80)
    
    for i, row in data.iterrows():
        current_spread = row['price_diff_pct']
        current_time = row['time']
        
        # 檢查進場條件
        if current_position is None:
            if current_spread <= entry_threshold:
                current_position = {
                    'entry_time': current_time,
                    'entry_spread': current_spread,
                    'trade_number': len(trades) + 1
                }
                print(f"📈 進場#{len(trades)+1} | {current_time} | 差價: {current_spread:.4f}%")
        
        # 檢查出場條件
        elif current_position is not None:
            # 止盈條件
            if current_spread >= take_profit:
                duration = (current_time - current_position['entry_time']).total_seconds() / 60
                spread_change = abs(current_position['entry_spread']) - abs(current_spread)
                
                trade_record = {
                    'entry_time': current_position['entry_time'],
                    'exit_time': current_time,
                    'entry_spread': current_position['entry_spread'],
                    'exit_spread': current_spread,
                    'spread_change': spread_change,
                    'duration_minutes': duration,
                    'exit_reason': 'take_profit'
                }
                trades.append(trade_record)
                
                print(f"✅ 出場#{current_position['trade_number']} | {current_time} | 差價: {current_spread:.4f}% | "
                      f"收窄: {spread_change:.4f}% | 持倉: {duration:.0f}分鐘")
                
                current_position = None
            
            # 止損條件
            elif current_spread >= stop_loss:
                duration = (current_time - current_position['entry_time']).total_seconds() / 60
                spread_change = abs(current_position['entry_spread']) - abs(current_spread)
                
                trade_record = {
                    'entry_time': current_position['entry_time'],
                    'exit_time': current_time,
                    'entry_spread': current_position['entry_spread'],
                    'exit_spread': current_spread,
                    'spread_change': spread_change,
                    'duration_minutes': duration,
                    'exit_reason': 'stop_loss'
                }
                trades.append(trade_record)
                
                print(f"❌ 止損#{current_position['trade_number']} | {current_time} | 差價: {current_spread:.4f}% | "
                      f"虧損: {spread_change:.4f}% | 持倉: {duration:.0f}分鐘")
                
                current_position = None
    
    # 如果最後還有持倉，強制平倉
    if current_position is not None:
        last_row = data.iloc[-1]
        duration = (last_row['time'] - current_position['entry_time']).total_seconds() / 60
        spread_change = abs(current_position['entry_spread']) - abs(last_row['price_diff_pct'])
        
        trade_record = {
            'entry_time': current_position['entry_time'],
            'exit_time': last_row['time'],
            'entry_spread': current_position['entry_spread'],
            'exit_spread': last_row['price_diff_pct'],
            'spread_change': spread_change,
            'duration_minutes': duration,
            'exit_reason': 'force_close'
        }
        trades.append(trade_record)
        
        print(f"🔚 強制平倉#{current_position['trade_number']} | {last_row['time']} | "
              f"差價: {last_row['price_diff_pct']:.4f}% | 持倉: {duration:.0f}分鐘")
    
    print(f"\n📊 交易統計:")
    print(f"總交易次數: {len(trades)}")
    
    if trades:
        trades_df = pd.DataFrame(trades)
        print(f"平均持倉時間: {trades_df['duration_minutes'].mean():.0f}分鐘")
        print(f"最長持倉時間: {trades_df['duration_minutes'].max():.0f}分鐘")
        print(f"最短持倉時間: {trades_df['duration_minutes'].min():.0f}分鐘")
        
        print(f"\n📋 出場原因:")
        exit_reasons = trades_df['exit_reason'].value_counts()
        for reason, count in exit_reasons.items():
            print(f"   {reason}: {count}次")
    
    # 分析為什麼交易次數少
    entry_points = data[data['price_diff_pct'] <= entry_threshold]
    print(f"\n🤔 分析:")
    print(f"符合進場條件的數據點: {len(entry_points)}個")
    print(f"實際交易次數: {len(trades)}次")
    print(f"差異: {len(entry_points) - len(trades)}個機會被錯過")
    
    # 檢查錯過的機會
    if len(trades) > 0:
        print(f"\n⏰ 交易時間段分析:")
        for i, trade in enumerate(trades):
            print(f"交易#{i+1}: {trade['entry_time']} 到 {trade['exit_time']} "
                  f"({trade['duration_minutes']:.0f}分鐘)")
            
            # 檢查這個時間段內有多少個符合條件的點被忽略
            trade_start = pd.to_datetime(trade['entry_time'])
            trade_end = pd.to_datetime(trade['exit_time'])
            
            ignored_points = entry_points[
                (entry_points['time'] > trade_start) & 
                (entry_points['time'] < trade_end)
            ]
            
            if len(ignored_points) > 0:
                print(f"   持倉期間忽略了 {len(ignored_points)} 個進場信號")

def main():
    debug_trading_logic()

if __name__ == "__main__":
    main()
