#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram 通知功能测试
"""

import os
import sys

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from telegram_notifier import TelegramNotifier

def test_arbitrage_opportunity():
    """测试套利机会通知"""
    print("🧪 测试套利机会通知...")
    
    notifier = TelegramNotifier()
    
    success = notifier.send_arbitrage_opportunity(
        bybit_price=3300.0,
        mt5_price=3315.0,
        spread=15.0,
        percentage_spread=0.45,
        funding_rate=-0.0001
    )
    
    if success:
        print("✅ 套利机会通知发送成功")
    else:
        print("❌ 套利机会通知发送失败")
    
    return success

def test_trade_execution():
    """测试交易执行通知"""
    print("🧪 测试交易执行通知...")
    
    notifier = TelegramNotifier()
    
    trade_info = {
        'trade_id': 'TEST123',
        'bybit_side': 'Buy',
        'mt5_side': 'Sell',
        'bybit_qty': 0.1,
        'mt5_qty': 0.1,
        'bybit_price': 3300.0,
        'mt5_price': 3315.0,
        'bybit_margin': 330.0,
        'mt5_margin': 331.5,
        'bybit_fee': 0.33,
        'mt5_fee': 0.33,
        'total_margin': 661.5,
        'expected_profit': 15.0
    }
    
    success = notifier.send_trade_execution(trade_info)
    
    if success:
        print("✅ 交易执行通知发送成功")
    else:
        print("❌ 交易执行通知发送失败")
    
    return success

def test_trade_close():
    """测试平仓通知"""
    print("🧪 测试平仓通知...")
    
    notifier = TelegramNotifier()
    
    close_info = {
        'trade_id': 'TEST123',
        'profit': 12.5,
        'profit_percentage': 1.89,
        'duration': '00:05:30'
    }
    
    success = notifier.send_trade_close(close_info)
    
    if success:
        print("✅ 平仓通知发送成功")
    else:
        print("❌ 平仓通知发送失败")
    
    return success

def main():
    """主测试函数"""
    print("🚀 Telegram 通知功能测试")
    print("=" * 50)
    
    results = []
    
    # 测试各种通知类型
    results.append(("套利机会通知", test_arbitrage_opportunity()))
    results.append(("交易执行通知", test_trade_execution()))
    results.append(("平仓通知", test_trade_close()))
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:<15}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有Telegram通知测试通过！")
    else:
        print("⚠️ 部分Telegram通知测试失败")

if __name__ == "__main__":
    main()
