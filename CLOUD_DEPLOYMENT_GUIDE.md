# XAU套利系统 - 云端部署指南

## 🌐 问题解决

### MetaTrader5包部署问题
**问题**: MetaTrader5包只能在Windows系统安装，云端Linux环境无法使用
**解决方案**: 创建云端兼容的MT5客户端模拟器

## 📋 云端部署步骤

### 1. 准备文件
确保以下文件存在：
- `requirements-cloud.txt` - 云端专用依赖（不含MetaTrader5）
- `bybit_mt5_client_cloud.py` - 云端兼容的MT5客户端
- `cloud_config.py` - 环境自动检测配置
- `start_cloud_strategy.py` - 云端专用启动脚本

### 2. Railway部署（推荐）

#### 2.1 方法一：GitHub连接（推荐）
1. **Fork仓库**: 访问 https://github.com/YCRicky/XAU_ARB 并Fork到你的账户
2. **连接Railway**:
   - 访问 https://railway.app
   - 选择 "Deploy from GitHub repo"
   - 选择你Fork的XAU_ARB仓库
3. **配置环境变量**:
   ```
   BYBIT_API_KEY=你的Bybit API密钥
   BYBIT_API_SECRET=你的Bybit API密钥
   TELEGRAM_BOT_TOKEN=你的Telegram机器人Token
   TELEGRAM_CHAT_ID=你的Telegram聊天ID
   ```
4. **部署**: Railway会自动使用 `railway.toml` 配置进行部署

#### 2.2 方法二：CLI部署
```bash
# 1. 克隆仓库
git clone https://github.com/YCRicky/XAU_ARB.git
cd XAU_ARB

# 2. 安装Railway CLI
npm install -g @railway/cli

# 3. 登录并初始化
railway login
railway init

# 4. 设置环境变量
railway variables set BYBIT_API_KEY=你的密钥
railway variables set BYBIT_API_SECRET=你的密钥
railway variables set TELEGRAM_BOT_TOKEN=你的Token
railway variables set TELEGRAM_CHAT_ID=你的ID

# 5. 部署
railway up
```

#### 2.3 部署配置说明
系统已包含以下配置文件：
- `railway.toml` - Railway部署配置
- `runtime.txt` - Python版本指定
- `Dockerfile` - Docker容器配置
- `deploy_cloud.py` - 云端部署启动器
- `requirements-cloud.txt` - 云端专用依赖（无MetaTrader5）

#### 2.4 故障排除
如果遇到构建错误：

**方法1: 使用Dockerfile部署**
1. 在Railway项目设置中选择 "Deploy from Dockerfile"
2. 系统会自动使用 `Dockerfile` 进行构建

**方法2: 手动指定构建命令**
在Railway环境变量中添加：
```
NIXPACKS_BUILD_CMD=pip install -r requirements-cloud.txt
NIXPACKS_START_CMD=python deploy_cloud.py
```

### 3. Heroku部署

#### 3.1 创建Procfile
```
worker: python start_cloud_strategy.py
```

#### 3.2 创建runtime.txt
```
python-3.9.18
```

#### 3.3 部署命令
```bash
heroku create your-app-name
heroku config:set BYBIT_API_KEY=你的密钥
heroku config:set BYBIT_API_SECRET=你的密钥
heroku config:set TELEGRAM_BOT_TOKEN=你的Token
heroku config:set TELEGRAM_CHAT_ID=你的ID
git push heroku main
heroku ps:scale worker=1
```

### 4. Render部署

#### 4.1 创建render.yaml
```yaml
services:
  - type: worker
    name: xau-arbitrage
    env: python
    buildCommand: pip install -r requirements-cloud.txt
    startCommand: python start_cloud_strategy.py
    envVars:
      - key: BYBIT_API_KEY
        sync: false
      - key: BYBIT_API_SECRET
        sync: false
      - key: TELEGRAM_BOT_TOKEN
        sync: false
      - key: TELEGRAM_CHAT_ID
        sync: false
```

## 🔧 云端兼容特性

### 自动环境检测
系统会自动检测运行环境：
- **Windows本地**: 使用真实MetaTrader5客户端
- **云端Linux**: 使用模拟MT5客户端

### MT5模拟客户端功能
- ✅ 价格获取（可接入外汇API）
- ✅ 模拟交易执行
- ✅ 持仓管理
- ✅ 账户余额追踪
- ✅ 市场开放检测

### 价格数据源
云端版本支持多种价格数据源：
1. **Alpha Vantage API** (免费，需注册)
2. **模拟价格** (基于Bybit价格调整)
3. **其他外汇API** (可扩展)

## ⚙️ 配置说明

### 环境变量
```bash
# 必需的API密钥
BYBIT_API_KEY=你的Bybit API密钥
BYBIT_API_SECRET=你的Bybit API密钥
TELEGRAM_BOT_TOKEN=你的Telegram机器人Token
TELEGRAM_CHAT_ID=你的Telegram聊天ID

# 可选的外汇API密钥
ALPHA_VANTAGE_API_KEY=你的Alpha Vantage API密钥
```

### 依赖文件选择
- **本地Windows**: 使用 `requirements.txt`
- **云端Linux**: 使用 `requirements-cloud.txt`

## 🚀 启动方式

### 本地启动
```bash
python start_new_strategy.py
```

### 云端启动
```bash
python start_cloud_strategy.py
```

### 环境检测
```bash
python cloud_config.py
```

## 📊 功能对比

| 功能 | 本地版本 | 云端版本 |
|------|----------|----------|
| Bybit交易 | ✅ 真实 | ✅ 真实 |
| MT5交易 | ✅ 真实 | ✅ 模拟 |
| 价格获取 | ✅ 真实 | ✅ API/模拟 |
| 套利检测 | ✅ | ✅ |
| 风控系统 | ✅ | ✅ |
| Telegram通知 | ✅ | ✅ |
| 每日报告 | ✅ | ✅ |
| 24/7运行 | ❌ | ✅ |

## ⚠️ 重要提醒

### 云端部署注意事项
1. **MT5为模拟交易** - 云端版本的MT5部分是模拟的
2. **价格数据准确性** - 建议配置真实的外汇API
3. **网络稳定性** - 确保云端服务商网络稳定
4. **监控重要性** - 密切关注Telegram通知

### 建议部署流程
1. **本地测试** - 先在本地Windows环境完整测试
2. **云端测试** - 使用小资金在云端测试
3. **逐步放大** - 确认稳定后逐步增加资金
4. **持续监控** - 通过Telegram密切监控

## 🎯 部署完成检查

部署成功后，你应该收到：
1. ✅ 系统启动通知
2. ✅ 环境检测信息
3. ✅ 每日8:00报告（次日开始）
4. ✅ 实时交易通知

**GitHub仓库**: https://github.com/YCRicky/XAU_ARB

现在可以安全地部署到云端了！🌐
