#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def show_api_key_guide():
    """顯示 API Key 修復指南"""
    
    print("=" * 60)
    print("🔧 Bybit API Key 修復指南")
    print("=" * 60)
    print()
    print("❌ 問題診斷：")
    print("   你的 API Key 長度只有 18 字符，太短了！")
    print("   正常的 Bybit API Key 通常是 20-50 字符")
    print()
    print("✅ 解決方案：重新生成 API Key")
    print()
    print("📋 步驟：")
    print("1. 登入 Bybit 官網 (https://www.bybit.com)")
    print("2. 進入 API Management 頁面")
    print("3. 刪除現有的 API Key")
    print("4. 創建新的 API Key，確保以下權限：")
    print("   ✅ Contract Trade (合約交易)")
    print("   ✅ Read (讀取)")
    print("   ✅ Transfer (轉帳) - 可選")
    print("5. 複製完整的 API Key 和 Secret")
    print("6. 更新 config.env 文件")
    print()
    print("⚠️ 重要提醒：")
    print("- API Key 通常以 'UsihdqGP2H7z3AXiZD' 開頭，但後面還有更多字符")
    print("- 確保複製時沒有遺漏任何字符")
    print("- 檢查 config.env 文件中沒有多餘的空格或換行符")
    print()
    print("🔍 驗證方法：")
    print("重新生成後，API Key 長度應該在 20-50 字符之間")
    print("然後重新運行測試腳本")

if __name__ == "__main__":
    show_api_key_guide() 