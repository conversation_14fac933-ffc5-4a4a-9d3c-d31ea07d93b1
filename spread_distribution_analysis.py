"""
差價分布統計分析
分析負差價的90%信心水準和極值分布
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

def analyze_spread_distribution():
    """分析差價分布統計"""
    
    # 載入數據
    try:
        data = pd.read_csv('data/xau_spread_analysis_simple.csv')
        print(f"✅ 成功載入 {len(data)} 筆數據")
    except FileNotFoundError:
        print("❌ 找不到數據文件")
        return
    
    spreads = data['price_diff_pct'].values
    
    print("\n" + "="*80)
    print("XAU差價分布統計分析")
    print("="*80)
    
    # 基本統計
    print(f"📊 基本統計:")
    print(f"   總數據點: {len(spreads):,}")
    print(f"   平均差價: {np.mean(spreads):.4f}%")
    print(f"   標準差: {np.std(spreads):.4f}%")
    print(f"   最小值: {np.min(spreads):.4f}%")
    print(f"   最大值: {np.max(spreads):.4f}%")
    
    # 分離正負差價
    negative_spreads = spreads[spreads < 0]
    positive_spreads = spreads[spreads > 0]
    
    print(f"\n📉 負差價統計 (Bybit < MT5):")
    print(f"   負差價數量: {len(negative_spreads):,} ({len(negative_spreads)/len(spreads)*100:.1f}%)")
    print(f"   負差價平均: {np.mean(negative_spreads):.4f}%")
    print(f"   負差價標準差: {np.std(negative_spreads):.4f}%")
    print(f"   最小負差價: {np.min(negative_spreads):.4f}%")
    
    print(f"\n📈 正差價統計 (Bybit > MT5):")
    print(f"   正差價數量: {len(positive_spreads):,} ({len(positive_spreads)/len(spreads)*100:.1f}%)")
    print(f"   正差價平均: {np.mean(positive_spreads):.4f}%")
    print(f"   正差價標準差: {np.std(positive_spreads):.4f}%")
    print(f"   最大正差價: {np.max(positive_spreads):.4f}%")
    
    # 百分位數分析
    print(f"\n📊 整體差價百分位數:")
    percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
    for p in percentiles:
        value = np.percentile(spreads, p)
        print(f"   {p:2d}%: {value:7.4f}%")
    
    # 負差價的詳細百分位數
    print(f"\n📉 負差價百分位數分析:")
    if len(negative_spreads) > 0:
        neg_percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
        for p in neg_percentiles:
            value = np.percentile(negative_spreads, p)
            print(f"   {p:2d}%: {value:7.4f}%")
    
    # 90%信心水準分析
    print(f"\n🎯 90%信心水準分析:")
    
    # 整體90%信心區間
    lower_5 = np.percentile(spreads, 5)
    upper_95 = np.percentile(spreads, 95)
    print(f"   整體90%信心區間: [{lower_5:.4f}%, {upper_95:.4f}%]")
    
    # 負差價90%信心區間
    if len(negative_spreads) > 0:
        neg_lower_5 = np.percentile(negative_spreads, 5)
        neg_upper_95 = np.percentile(negative_spreads, 95)
        print(f"   負差價90%信心區間: [{neg_lower_5:.4f}%, {neg_upper_95:.4f}%]")
        
        # 負差價的極值分析
        print(f"\n⚠️  負差價極值分析:")
        print(f"   5%極值 (更負): {neg_lower_5:.4f}% (有5%機會更負)")
        print(f"   10%極值: {np.percentile(negative_spreads, 10):.4f}%")
        print(f"   1%極值 (最負): {np.percentile(negative_spreads, 1):.4f}% (有1%機會更負)")
    
    # 正差價90%信心區間
    if len(positive_spreads) > 0:
        pos_lower_5 = np.percentile(positive_spreads, 5)
        pos_upper_95 = np.percentile(positive_spreads, 95)
        print(f"   正差價90%信心區間: [{pos_lower_5:.4f}%, {pos_upper_95:.4f}%]")
    
    # 交易機會分析
    print(f"\n💰 交易機會分析 (基於90%信心水準):")
    
    # 不同閾值的機會統計
    thresholds = [-0.1, -0.2, -0.3, -0.4, -0.5, -0.6, -0.7, -0.8, -0.9, -1.0]
    
    print(f"   負差價進場機會:")
    for threshold in thresholds:
        count = np.sum(spreads <= threshold)
        percentage = count / len(spreads) * 100
        daily_avg = count / 7  # 7天數據
        print(f"   <= {threshold:4.1f}%: {count:4d}次 ({percentage:5.2f}%) - 平均每天{daily_avg:.1f}次")
    
    # 基於90%信心水準的建議
    neg_90_threshold = np.percentile(negative_spreads, 90) if len(negative_spreads) > 0 else 0
    print(f"\n🎯 基於90%信心水準的建議:")
    print(f"   負差價90%分位數: {neg_90_threshold:.4f}%")
    print(f"   建議進場閾值: {neg_90_threshold:.4f}% (有10%機會出現更負的差價)")
    
    # 風險評估
    print(f"\n⚠️  風險評估:")
    extreme_negative = np.percentile(negative_spreads, 1) if len(negative_spreads) > 0 else 0
    print(f"   極端負差價 (1%): {extreme_negative:.4f}%")
    print(f"   如果以 {neg_90_threshold:.4f}% 進場，最大可能虧損到 {extreme_negative:.4f}%")
    print(f"   風險差額: {abs(extreme_negative - neg_90_threshold):.4f}%")
    
    # 保存分析結果
    results = {
        'total_points': len(spreads),
        'negative_count': len(negative_spreads),
        'positive_count': len(positive_spreads),
        'negative_percentage': len(negative_spreads)/len(spreads)*100,
        'overall_90_ci': [lower_5, upper_95],
        'negative_90_ci': [neg_lower_5, neg_upper_95] if len(negative_spreads) > 0 else None,
        'negative_90_threshold': neg_90_threshold,
        'extreme_negative_1pct': extreme_negative,
        'risk_range': abs(extreme_negative - neg_90_threshold) if len(negative_spreads) > 0 else 0
    }
    
    # 保存到文件
    import json
    with open('data/spread_distribution_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 分析結果已保存到: data/spread_distribution_analysis.json")
    
    return results

def main():
    """主函數"""
    analyze_spread_distribution()

if __name__ == "__main__":
    main()
