#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整測試 Bybit 和 MT5 下單功能
"""

import os
import sys
import time
from dotenv import load_dotenv
from bybit_futures_client import BybitFuturesClient
from bybit_mt5_client import BybitMT5Client

load_dotenv('config.env')

def test_complete_trading():
    """完整測試交易功能"""
    print("🚀 開始完整交易功能測試...")
    
    # 1. 測試 Bybit 連接和下單
    print("\n" + "="*50)
    print("1. 測試 Bybit 下單功能")
    print("="*50)
    
    bybit_client = BybitFuturesClient()
    
    # 檢查帳戶資訊
    account_info = bybit_client.get_account_info()
    if account_info:
        wallet_list = account_info.get("list", [])
        if wallet_list:
            wallet = wallet_list[0]
            total_balance = float(wallet.get("totalWalletBalance", 0))
            available_balance = float(wallet.get("totalAvailableBalance", 0))
            print(f"✅ Bybit 帳戶資訊:")
            print(f"   總餘額: {total_balance:.2f} USDT")
            print(f"   可用餘額: {available_balance:.2f} USDT")
            
            # 計算最小下單量
            min_qty = 0.001  # 最小下單量
            required_margin = min_qty * 3315 * 0.05  # 約 5% 保證金
            
            if available_balance >= required_margin:
                print(f"✅ 餘額充足，可以下單")
                print(f"   最小下單量: {min_qty}")
                print(f"   所需保證金: {required_margin:.2f} USDT")
                
                # 嘗試下單
                result = bybit_client.place_order(
                    symbol="XAUTUSDT",
                    side="Buy",
                    qty=min_qty,
                    order_type="Market"
                )
                
                if result:
                    print("🎉 Bybit 下單成功！")
                else:
                    print("❌ Bybit 下單失敗")
            else:
                print(f"❌ 餘額不足，無法下單")
                print(f"   需要: {required_margin:.2f} USDT")
                print(f"   可用: {available_balance:.2f} USDT")
    
    # 2. 測試 MT5 連接和下單
    print("\n" + "="*50)
    print("2. 測試 MT5 下單功能")
    print("="*50)
    
    mt5_client = BybitMT5Client()
    
    # 檢查連接
    if mt5_client.connect():
        print("✅ MT5 連接成功")
        
        # 檢查帳戶資訊
        account_info = mt5_client.get_account_info()
        if account_info:
            print(f"✅ MT5 帳戶資訊:")
            print(f"   帳號: {account_info.get('login', 'N/A')}")
            print(f"   餘額: {account_info.get('balance', 0):.2f}")
            print(f"   權益: {account_info.get('equity', 0):.2f}")
        
        # 獲取價格
        tick = mt5_client.get_tick("XAUUSD+")
        if tick:
            print(f"✅ XAUUSD+ 價格:")
            print(f"   買價: {tick['bid']}")
            print(f"   賣價: {tick['ask']}")
            
            # 嘗試下單
            result = mt5_client.place_order(
                symbol="XAUUSD+",
                order_type=0,  # Buy
                volume=0.01,   # 最小下單量
                comment="Test Order"
            )
            
            if result:
                print("🎉 MT5 下單成功！")
                print(f"   訂單號: {result.get('order', 'N/A')}")
                
                # 等待一下
                time.sleep(2)
                
                # 查詢持倉
                positions = mt5_client.get_positions("XAUUSD+")
                if positions:
                    print(f"✅ 持倉查詢成功，找到 {len(positions)} 個持倉")
                    for pos in positions:
                        print(f"   持倉號: {pos.get('ticket', 'N/A')}")
                        print(f"   數量: {pos.get('volume', 0)}")
                        print(f"   類型: {'買入' if pos.get('type', 0) == 0 else '賣出'}")
                        
                        # 嘗試平倉
                        close_result = mt5_client.close_position(
                            pos.get('ticket', 0),
                            pos.get('volume', 0),
                            pos.get('type', 0) == 0  # 如果是買入，平倉時賣出
                        )
                        
                        if close_result:
                            print("🎉 平倉成功！")
                        else:
                            print("❌ 平倉失敗")
                else:
                    print("ℹ️ 沒有找到持倉")
            else:
                print("❌ MT5 下單失敗")
        else:
            print("❌ 無法獲取 MT5 價格")
    else:
        print("❌ MT5 連接失敗")
    
    print("\n" + "="*50)
    print("測試完成")
    print("="*50)

if __name__ == "__main__":
    test_complete_trading() 