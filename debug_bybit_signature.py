#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import hmac
import hashlib
import json
import requests
from dotenv import load_dotenv

load_dotenv('config.env')

def test_bybit_signature():
    """測試 Bybit API 簽名邏輯"""
    
    api_key = os.getenv('BYBIT_API_KEY')
    api_secret = os.getenv('BYBIT_API_SECRET')
    
    print("=== Bybit API 簽名測試 ===")
    print(f"API Key: {api_key}")
    print(f"API Secret: {api_secret[:10]}..." if api_secret else "API Secret: None")
    print(f"API Key 長度: {len(api_key) if api_key else 0}")
    print(f"API Secret 長度: {len(api_secret) if api_secret else 0}")
    print()
    
    if not api_key or not api_secret:
        print("❌ API Key 或 Secret 未設定")
        return
    
    # 測試參數
    timestamp = str(int(time.time() * 1000))
    recv_window = 10000
    
    # 測試1: 設置槓桿
    print("=== 測試1: 設置槓桿 ===")
    endpoint = "/v5/position/set-leverage"
    params = {
        "category": "linear",
        "symbol": "XAUTUSDT",
        "leverage": "20",
        "recvWindow": recv_window
    }
    
    body = json.dumps(params, separators=(",", ":"), ensure_ascii=False)
    origin_string = timestamp + api_key + str(recv_window) + body
    signature = hmac.new(api_secret.encode(), origin_string.encode(), hashlib.sha256).hexdigest()
    
    print(f"Timestamp: {timestamp}")
    print(f"RecvWindow: {recv_window}")
    print(f"Body: {body}")
    print(f"Origin String: {origin_string}")
    print(f"Signature: {signature}")
    print()
    
    headers = {
        "X-BAPI-API-KEY": api_key,
        "X-BAPI-TIMESTAMP": timestamp,
        "X-BAPI-SIGN": signature,
        "X-BAPI-RECV-WINDOW": str(recv_window),
        "Content-Type": "application/json"
    }
    
    url = "https://api.bybit.com" + endpoint
    try:
        response = requests.post(url, headers=headers, json=params, timeout=10)
        result = response.json()
        print(f"Response: {result}")
        print(f"Status Code: {response.status_code}")
    except Exception as e:
        print(f"請求失敗: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 測試2: 下單
    print("=== 測試2: 下單 ===")
    timestamp = str(int(time.time() * 1000))
    endpoint = "/v5/order/create"
    params = {
        "category": "linear",
        "symbol": "XAUTUSDT",
        "side": "Buy",
        "orderType": "Market",
        "qty": "0.001",
        "timeInForce": "GTC",
        "recvWindow": recv_window
    }
    
    body = json.dumps(params, separators=(",", ":"), ensure_ascii=False)
    origin_string = timestamp + api_key + str(recv_window) + body
    signature = hmac.new(api_secret.encode(), origin_string.encode(), hashlib.sha256).hexdigest()
    
    print(f"Timestamp: {timestamp}")
    print(f"RecvWindow: {recv_window}")
    print(f"Body: {body}")
    print(f"Origin String: {origin_string}")
    print(f"Signature: {signature}")
    print()
    
    headers = {
        "X-BAPI-API-KEY": api_key,
        "X-BAPI-TIMESTAMP": timestamp,
        "X-BAPI-SIGN": signature,
        "X-BAPI-RECV-WINDOW": str(recv_window),
        "Content-Type": "application/json"
    }
    
    url = "https://api.bybit.com" + endpoint
    try:
        response = requests.post(url, headers=headers, json=params, timeout=10)
        result = response.json()
        print(f"Response: {result}")
        print(f"Status Code: {response.status_code}")
    except Exception as e:
        print(f"請求失敗: {e}")

if __name__ == "__main__":
    test_bybit_signature() 