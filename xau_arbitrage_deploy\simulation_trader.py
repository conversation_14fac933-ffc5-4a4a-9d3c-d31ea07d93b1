#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XAU套利模擬交易系統
使用真實價格數據進行模擬交易，避免實際資金風險
"""

import os
import sys
import json
import time
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
load_dotenv('config.env')

from bybit_futures_client import BybitFuturesClient
from cloud_config import get_mt5_client
from telegram_notifier import TelegramNotifier

class SimulationAccount:
    """模擬帳戶類"""
    def __init__(self, initial_balance: float, account_type: str, leverage: int = 1):
        self.account_type = account_type  # 'bybit' or 'mt5'
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.equity = initial_balance
        self.margin_used = 0.0
        self.free_margin = initial_balance
        self.leverage = leverage
        self.positions = {}  # position_id -> position_info
        self.trade_history = []
        self.created_at = datetime.now()
        
    def get_account_info(self):
        """獲取帳戶信息"""
        return {
            'balance': self.balance,
            'equity': self.equity,
            'margin_used': self.margin_used,
            'free_margin': self.free_margin,
            'margin_level': (self.equity / self.margin_used * 100) if self.margin_used > 0 else 0,
            'leverage': self.leverage,
            'positions_count': len(self.positions)
        }
    
    def add_position(self, position_id: str, position_info: Dict):
        """添加倉位"""
        self.positions[position_id] = position_info
        self.update_margin()
    
    def remove_position(self, position_id: str):
        """移除倉位"""
        if position_id in self.positions:
            del self.positions[position_id]
            self.update_margin()
    
    def update_margin(self):
        """更新保證金使用情況"""
        total_margin = 0
        for pos in self.positions.values():
            total_margin += pos.get('margin_required', 0)
        
        self.margin_used = total_margin
        self.free_margin = self.balance - self.margin_used
    
    def update_equity(self, current_prices: Dict):
        """更新淨值（考慮未實現盈虧）"""
        total_unrealized_pnl = 0
        
        for pos in self.positions.values():
            if self.account_type == 'bybit':
                current_price = current_prices.get('bybit', pos['entry_price'])
                pnl = (current_price - pos['entry_price']) * pos['size']
                if pos['side'] == 'Sell':
                    pnl = -pnl
            else:  # mt5
                current_price = current_prices.get('mt5', pos['entry_price'])
                pnl = (current_price - pos['entry_price']) * pos['size'] * 100  # MT5: 手數*100盎司
                if pos['side'] == 'Sell':
                    pnl = -pnl
            
            pos['unrealized_pnl'] = pnl
            total_unrealized_pnl += pnl
        
        self.equity = self.balance + total_unrealized_pnl

class XAUSimulationTrader:
    """XAU套利模擬交易器"""
    
    def __init__(self):
        # 初始化真實數據客戶端
        self.bybit_client = BybitFuturesClient()
        MT5ClientClass = get_mt5_client()
        self.mt5_client = MT5ClientClass()
        self.notifier = TelegramNotifier()
        
        # 交易設定
        self.min_spread_threshold = 0.2  # 進場價差閾值 0.2%
        self.close_spread_threshold = 0.05  # 平倉價差閾值 0.05%
        self.risk_spread_threshold = 0.5  # 風控價差閾值 0.5%
        self.max_positions = 5  # 最多同時持有5個套利倉位
        
        # 🚨 修正後的下單邏輯：以Bybit為基準
        self.base_bybit_size = 1.0  # 基準：1張XAUTUSDT
        self.base_mt5_size = 0.01   # 對應：0.01手XAUUSD+ (1盎司)
        
        # 模擬帳戶設置
        self.bybit_account = SimulationAccount(10000.0, 'bybit', leverage=20)  # 10000U, 20倍槓桿
        self.mt5_account = SimulationAccount(1000.0, 'mt5', leverage=500)      # 1000U, 500倍槓桿
        
        # 交易狀態
        self.active_trades = {}
        self.trade_counter = 0
        self.total_trades = 0
        
        # 數據記錄
        self.performance_data = []
        self.equity_curve = []
        
        # 文件路徑
        self.simulation_file = 'data/simulation_results.json'
        self.performance_file = 'data/simulation_performance.csv'
        self.ensure_data_directory()
        
        # 設置日誌
        self.setup_logging()
        
        print("🎮 XAU套利模擬交易系統已初始化")
        print(f"💰 Bybit模擬帳戶: {self.bybit_account.balance:,.2f} USDT (20x槓桿)")
        print(f"💰 MT5模擬帳戶: {self.mt5_account.balance:,.2f} USD (500x槓桿)")
        print(f"📊 基準下單量: Bybit {self.base_bybit_size} 張, MT5 {self.base_mt5_size} 手")
    
    def ensure_data_directory(self):
        """確保數據目錄存在"""
        os.makedirs('data', exist_ok=True)
    
    def setup_logging(self):
        """設置日誌"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('simulation_trader.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_real_prices(self) -> Optional[Dict[str, float]]:
        """獲取真實價格數據"""
        prices = {}
        
        try:
            # 獲取Bybit真實價格
            bybit_price = self.bybit_client.get_ticker_price("XAUTUSDT")
            if bybit_price:
                prices['bybit'] = bybit_price
            else:
                self.logger.error("無法獲取Bybit價格")
                return None
            
            # 獲取MT5真實價格
            if self.mt5_client.connect():
                mt5_price = self.mt5_client.get_current_price("XAUUSD+")
                if mt5_price:
                    prices['mt5'] = mt5_price
                    self.mt5_client.disconnect()
                else:
                    self.logger.error("無法獲取MT5價格")
                    self.mt5_client.disconnect()
                    return None
            else:
                self.logger.error("無法連接MT5")
                return None
            
            return prices
            
        except Exception as e:
            self.logger.error(f"獲取價格失敗: {e}")
            return None
    
    def calculate_spread(self, bybit_price: float, mt5_price: float) -> Dict:
        """計算價差"""
        absolute_spread = mt5_price - bybit_price
        percentage_spread = (absolute_spread / bybit_price) * 100

        return {
            'absolute': absolute_spread,
            'percentage': percentage_spread,
            'direction': 'MT5_higher' if absolute_spread > 0 else 'Bybit_higher'
        }

    def calculate_position_sizes(self, prices: Dict) -> Dict:
        """🚨 修正後的倉位計算：以Bybit為基準"""
        bybit_price = prices['bybit']
        mt5_price = prices['mt5']

        # 🎯 核心邏輯：固定下單量，確保盎司數匹配
        bybit_qty = self.base_bybit_size  # 1張 = 1盎司
        mt5_qty = self.base_mt5_size      # 0.01手 = 1盎司

        # 計算所需保證金
        bybit_notional = bybit_qty * bybit_price  # 名義價值
        mt5_notional = mt5_qty * 100 * mt5_price  # 名義價值

        # Bybit保證金計算 (20倍槓桿)
        bybit_margin = bybit_notional / self.bybit_account.leverage

        # MT5保證金計算 (500倍槓桿)
        mt5_margin = mt5_notional / self.mt5_account.leverage

        # 檢查保證金是否足夠
        if bybit_margin > self.bybit_account.free_margin:
            self.logger.error(f"Bybit保證金不足: 需要{bybit_margin:.2f}, 可用{self.bybit_account.free_margin:.2f}")
            return None

        if mt5_margin > self.mt5_account.free_margin:
            self.logger.error(f"MT5保證金不足: 需要{mt5_margin:.2f}, 可用{self.mt5_account.free_margin:.2f}")
            return None

        return {
            'bybit_qty': bybit_qty,
            'mt5_qty': mt5_qty,
            'bybit_notional': bybit_notional,
            'mt5_notional': mt5_notional,
            'bybit_margin': bybit_margin,
            'mt5_margin': mt5_margin,
            'total_margin': bybit_margin + mt5_margin
        }

    def simulate_order_execution(self, side: str, qty: float, price: float, account_type: str) -> Dict:
        """模擬下單執行"""
        # 模擬手續費
        if account_type == 'bybit':
            fee_rate = 0.0006  # 0.06% taker fee
            fee = qty * price * fee_rate
        else:  # mt5
            fee_rate = 0.00003  # 0.003% spread cost
            fee = qty * 100 * price * fee_rate  # MT5: 手數*100盎司

        # 模擬滑點 (0.01%)
        slippage_rate = 0.0001
        if side in ['Buy', 'buy']:
            execution_price = price * (1 + slippage_rate)
        else:
            execution_price = price * (1 - slippage_rate)

        order_id = f"{account_type}_{int(time.time() * 1000)}"

        return {
            'order_id': order_id,
            'symbol': 'XAUTUSDT' if account_type == 'bybit' else 'XAUUSD+',
            'side': side,
            'qty': qty,
            'price': execution_price,
            'fee': fee,
            'status': 'filled',
            'timestamp': datetime.now()
        }

    def execute_arbitrage_trade(self, spread_info: Dict, prices: Dict) -> Optional[Dict]:
        """執行套利交易（模擬）"""
        if len(self.active_trades) >= self.max_positions:
            self.logger.warning("已達最大倉位限制")
            return None

        # 計算倉位大小
        position_sizes = self.calculate_position_sizes(prices)
        if not position_sizes:
            return None

        # 確定交易方向
        if spread_info['direction'] == 'MT5_higher':
            # MT5價格較高：賣MT5，買Bybit
            bybit_side = 'Buy'
            mt5_side = 'Sell'
        else:
            # Bybit價格較高：買MT5，賣Bybit
            bybit_side = 'Sell'
            mt5_side = 'Buy'

        self.trade_counter += 1
        trade_id = f"SIM_{self.trade_counter:04d}"

        try:
            # 模擬同時下單
            bybit_order = self.simulate_order_execution(
                bybit_side, position_sizes['bybit_qty'], prices['bybit'], 'bybit'
            )

            mt5_order = self.simulate_order_execution(
                mt5_side, position_sizes['mt5_qty'], prices['mt5'], 'mt5'
            )

            # 更新模擬帳戶
            bybit_position = {
                'position_id': f"{trade_id}_bybit",
                'symbol': 'XAUTUSDT',
                'side': bybit_side,
                'size': position_sizes['bybit_qty'],
                'entry_price': bybit_order['price'],
                'margin_required': position_sizes['bybit_margin'],
                'unrealized_pnl': 0,
                'timestamp': datetime.now()
            }

            mt5_position = {
                'position_id': f"{trade_id}_mt5",
                'symbol': 'XAUUSD+',
                'side': mt5_side,
                'size': position_sizes['mt5_qty'],
                'entry_price': mt5_order['price'],
                'margin_required': position_sizes['mt5_margin'],
                'unrealized_pnl': 0,
                'timestamp': datetime.now()
            }

            # 添加到模擬帳戶
            self.bybit_account.add_position(bybit_position['position_id'], bybit_position)
            self.mt5_account.add_position(mt5_position['position_id'], mt5_position)

            # 扣除手續費
            self.bybit_account.balance -= bybit_order['fee']
            self.mt5_account.balance -= mt5_order['fee']

            # 記錄交易
            trade_result = {
                'trade_id': trade_id,
                'timestamp': datetime.now(),
                'spread_percentage': spread_info['percentage'],
                'bybit_order': bybit_order,
                'mt5_order': mt5_order,
                'bybit_position': bybit_position,
                'mt5_position': mt5_position,
                'total_fees': bybit_order['fee'] + mt5_order['fee'],
                'status': 'active'
            }

            self.active_trades[trade_id] = trade_result
            self.total_trades += 1

            self.logger.info(f"✅ 模擬套利交易 {trade_id} 執行成功")
            self.logger.info(f"   Bybit: {bybit_side} {position_sizes['bybit_qty']} @ {bybit_order['price']:.2f}")
            self.logger.info(f"   MT5: {mt5_side} {position_sizes['mt5_qty']} @ {mt5_order['price']:.2f}")
            self.logger.info(f"   總手續費: ${trade_result['total_fees']:.2f}")

            return trade_result

        except Exception as e:
            self.logger.error(f"模擬交易執行失敗: {e}")
            return None

    def check_close_conditions(self, trade_info: Dict, current_prices: Dict) -> bool:
        """檢查平倉條件"""
        current_spread = self.calculate_spread(current_prices['bybit'], current_prices['mt5'])

        # 價差收斂到平倉閾值
        if abs(current_spread['percentage']) <= self.close_spread_threshold:
            return True

        # 風控條件：價差過大
        if abs(current_spread['percentage']) >= self.risk_spread_threshold:
            return True

        return False

    def close_arbitrage_trade(self, trade_info: Dict, current_prices: Dict) -> Optional[Dict]:
        """平倉套利交易（模擬）"""
        trade_id = trade_info['trade_id']

        try:
            # 獲取倉位信息
            bybit_position = trade_info['bybit_position']
            mt5_position = trade_info['mt5_position']

            # 確定平倉方向（與開倉相反）
            bybit_close_side = 'Sell' if bybit_position['side'] == 'Buy' else 'Buy'
            mt5_close_side = 'Sell' if mt5_position['side'] == 'Buy' else 'Buy'

            # 模擬平倉下單
            bybit_close_order = self.simulate_order_execution(
                bybit_close_side, bybit_position['size'], current_prices['bybit'], 'bybit'
            )

            mt5_close_order = self.simulate_order_execution(
                mt5_close_side, mt5_position['size'], current_prices['mt5'], 'mt5'
            )

            # 計算盈虧
            bybit_pnl = (bybit_close_order['price'] - bybit_position['entry_price']) * bybit_position['size']
            if bybit_position['side'] == 'Sell':
                bybit_pnl = -bybit_pnl

            mt5_pnl = (mt5_close_order['price'] - mt5_position['entry_price']) * mt5_position['size'] * 100
            if mt5_position['side'] == 'Sell':
                mt5_pnl = -mt5_pnl

            total_pnl = bybit_pnl + mt5_pnl
            total_fees = trade_info['total_fees'] + bybit_close_order['fee'] + mt5_close_order['fee']
            net_pnl = total_pnl - total_fees

            # 更新模擬帳戶
            self.bybit_account.balance += bybit_pnl - bybit_close_order['fee']
            self.mt5_account.balance += mt5_pnl - mt5_close_order['fee']

            # 移除倉位
            self.bybit_account.remove_position(bybit_position['position_id'])
            self.mt5_account.remove_position(mt5_position['position_id'])

            # 記錄平倉結果
            close_result = {
                'trade_id': trade_id,
                'close_timestamp': datetime.now(),
                'bybit_close_order': bybit_close_order,
                'mt5_close_order': mt5_close_order,
                'bybit_pnl': bybit_pnl,
                'mt5_pnl': mt5_pnl,
                'total_pnl': total_pnl,
                'total_fees': total_fees,
                'net_pnl': net_pnl,
                'duration': datetime.now() - trade_info['timestamp']
            }

            # 更新交易記錄
            trade_info.update(close_result)
            trade_info['status'] = 'closed'

            # 從活躍交易中移除
            if trade_id in self.active_trades:
                del self.active_trades[trade_id]

            self.logger.info(f"✅ 模擬套利交易 {trade_id} 平倉完成")
            self.logger.info(f"   Bybit盈虧: ${bybit_pnl:.2f}")
            self.logger.info(f"   MT5盈虧: ${mt5_pnl:.2f}")
            self.logger.info(f"   淨盈虧: ${net_pnl:.2f}")

            return close_result

        except Exception as e:
            self.logger.error(f"模擬平倉失敗: {e}")
            return None

    def update_performance_metrics(self, current_prices: Dict):
        """更新績效指標"""
        # 更新帳戶淨值
        self.bybit_account.update_equity(current_prices)
        self.mt5_account.update_equity(current_prices)

        # 計算總淨值
        total_equity = self.bybit_account.equity + self.mt5_account.equity
        initial_total = self.bybit_account.initial_balance + self.mt5_account.initial_balance

        # 記錄淨值曲線
        equity_point = {
            'timestamp': datetime.now(),
            'bybit_balance': self.bybit_account.balance,
            'bybit_equity': self.bybit_account.equity,
            'mt5_balance': self.mt5_account.balance,
            'mt5_equity': self.mt5_account.equity,
            'total_equity': total_equity,
            'total_return': ((total_equity - initial_total) / initial_total) * 100,
            'active_trades': len(self.active_trades)
        }

        self.equity_curve.append(equity_point)

        return equity_point

    def get_performance_summary(self) -> Dict:
        """獲取績效摘要"""
        if not self.equity_curve:
            return {}

        latest = self.equity_curve[-1]
        initial_total = self.bybit_account.initial_balance + self.mt5_account.initial_balance

        return {
            'total_trades': self.total_trades,
            'active_trades': len(self.active_trades),
            'initial_balance': initial_total,
            'current_equity': latest['total_equity'],
            'total_return_pct': latest['total_return'],
            'total_return_usd': latest['total_equity'] - initial_total,
            'bybit_account': self.bybit_account.get_account_info(),
            'mt5_account': self.mt5_account.get_account_info()
        }

    def save_simulation_data(self):
        """保存模擬數據"""
        data = {
            'simulation_info': {
                'start_time': self.bybit_account.created_at.isoformat(),
                'last_update': datetime.now().isoformat(),
                'total_trades': self.total_trades,
                'active_trades_count': len(self.active_trades)
            },
            'accounts': {
                'bybit': self.bybit_account.get_account_info(),
                'mt5': self.mt5_account.get_account_info()
            },
            'active_trades': self.active_trades,
            'performance_summary': self.get_performance_summary()
        }

        try:
            with open(self.simulation_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"保存模擬數據失敗: {e}")

    def run_simulation(self, duration_minutes: int = 60, check_interval: int = 60):
        """運行模擬交易"""
        print(f"🚀 開始運行模擬套利交易系統")
        print(f"⏰ 運行時長: {duration_minutes} 分鐘")
        print(f"🔄 檢查間隔: {check_interval} 秒")
        print("=" * 60)

        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)

        cycle_count = 0

        try:
            while datetime.now() < end_time:
                cycle_count += 1
                print(f"\n📊 第 {cycle_count} 次監控循環 ({datetime.now().strftime('%H:%M:%S')})")

                # 獲取真實價格
                current_prices = self.get_real_prices()
                if not current_prices:
                    print("❌ 無法獲取價格，跳過此次循環")
                    time.sleep(check_interval)
                    continue

                # 計算價差
                spread_info = self.calculate_spread(current_prices['bybit'], current_prices['mt5'])

                print(f"   💰 Bybit: ${current_prices['bybit']:.2f}")
                print(f"   💰 MT5: ${current_prices['mt5']:.2f}")
                print(f"   📈 價差: {spread_info['percentage']:.3f}%")
                print(f"   📊 活躍倉位: {len(self.active_trades)}/{self.max_positions}")

                # 更新績效指標
                performance = self.update_performance_metrics(current_prices)
                print(f"   💵 總淨值: ${performance['total_equity']:.2f} ({performance['total_return']:+.2f}%)")

                # 檢查平倉條件
                trades_to_close = []
                for trade_id, trade_info in self.active_trades.items():
                    if self.check_close_conditions(trade_info, current_prices):
                        trades_to_close.append(trade_id)

                # 執行平倉
                for trade_id in trades_to_close:
                    if trade_id in self.active_trades:
                        close_result = self.close_arbitrage_trade(self.active_trades[trade_id], current_prices)
                        if close_result:
                            print(f"   ✅ 平倉交易 {trade_id}, 淨盈虧: ${close_result['net_pnl']:+.2f}")

                # 檢查開倉條件
                if (len(self.active_trades) < self.max_positions and
                    abs(spread_info['percentage']) >= self.min_spread_threshold):

                    print(f"   🚨 發現套利機會！價差: {spread_info['percentage']:.3f}%")
                    trade_result = self.execute_arbitrage_trade(spread_info, current_prices)

                    if trade_result:
                        print(f"   ✅ 開倉交易 {trade_result['trade_id']}")
                    else:
                        print(f"   ❌ 開倉失敗")

                # 保存數據
                self.save_simulation_data()

                # 等待下次檢查
                time.sleep(check_interval)

        except KeyboardInterrupt:
            print("\n⏹️ 用戶中斷模擬")
        except Exception as e:
            print(f"\n❌ 模擬運行錯誤: {e}")

        # 最終報告
        print("\n" + "=" * 60)
        print("📊 模擬交易完成報告")
        print("=" * 60)

        final_summary = self.get_performance_summary()
        print(f"🔢 總交易次數: {final_summary['total_trades']}")
        print(f"📊 活躍倉位: {final_summary['active_trades']}")
        print(f"💰 初始資金: ${final_summary['initial_balance']:,.2f}")
        print(f"💵 當前淨值: ${final_summary['current_equity']:,.2f}")
        print(f"📈 總收益率: {final_summary['total_return_pct']:+.2f}%")
        print(f"💵 總收益: ${final_summary['total_return_usd']:+,.2f}")

        print(f"\n📊 Bybit帳戶:")
        bybit_info = final_summary['bybit_account']
        print(f"   餘額: ${bybit_info['balance']:,.2f}")
        print(f"   淨值: ${bybit_info['equity']:,.2f}")
        print(f"   已用保證金: ${bybit_info['margin_used']:,.2f}")

        print(f"\n📊 MT5帳戶:")
        mt5_info = final_summary['mt5_account']
        print(f"   餘額: ${mt5_info['balance']:,.2f}")
        print(f"   淨值: ${mt5_info['equity']:,.2f}")
        print(f"   已用保證金: ${mt5_info['margin_used']:,.2f}")

if __name__ == "__main__":
    # 創建模擬交易器
    simulator = XAUSimulationTrader()

    # 運行模擬（默認1小時）
    simulator.run_simulation(duration_minutes=60, check_interval=60)
