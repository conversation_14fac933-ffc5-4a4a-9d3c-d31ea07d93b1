#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小下單量測試 - 測試兩端基本下單功能
"""

import os
import sys
import time
from dotenv import load_dotenv
from bybit_futures_client import BybitFuturesClient
from bybit_mt5_client import BybitMT5Client

load_dotenv('config.env')

def test_minimal_trading():
    """測試最小下單量"""
    print("🔬 最小下單量測試...")
    
    # 1. 測試 Bybit 最小下單
    print("\n" + "="*40)
    print("1. Bybit 最小下單測試")
    print("="*40)
    
    bybit_client = BybitFuturesClient()
    
    # 檢查帳戶資訊
    account_info = bybit_client.get_account_info()
    if account_info:
        wallet_list = account_info.get("list", [])
        if wallet_list:
            wallet = wallet_list[0]
            total_balance = float(wallet.get("totalWalletBalance", 0))
            available_balance = float(wallet.get("totalAvailableBalance", 0))
            print(f"💰 Bybit 餘額:")
            print(f"   總餘額: {total_balance:.2f} USDT")
            print(f"   可用餘額: {available_balance:.2f} USDT")
            
            if available_balance > 0:
                # 嘗試最小下單量 - 確保名義價值 ≥ 5 USDT
                current_price = bybit_client.get_ticker_price("XAUTUSDT")
                if current_price:
                    min_qty = max(0.001, 5.0 / current_price)  # 至少 5 USDT 價值
                    min_qty = round(min_qty, 3)  # 保留3位小數
                    print(f"💰 當前價格: {current_price:.2f}, 計算下單量: {min_qty:.3f}")
                else:
                    min_qty = 0.002  # 備用值
                
                result = bybit_client.place_order(
                    symbol="XAUTUSDT",
                    side="Buy",
                    qty=min_qty,
                    order_type="Market"
                )
                
                if result:
                    print("🎉 Bybit 最小下單成功！")
                else:
                    print("❌ Bybit 最小下單失敗")
            else:
                print("❌ 可用餘額為 0，無法下單")
                print("💡 請檢查是否有其他持倉佔用資金，或充值更多資金")
    
    # 2. 測試 MT5 最小下單
    print("\n" + "="*40)
    print("2. MT5 最小下單測試")
    print("="*40)
    
    mt5_client = BybitMT5Client()
    
    if mt5_client.connect():
        print("✅ MT5 連接成功")
        
        # 獲取價格
        tick = mt5_client.get_tick("XAUUSD+")
        if tick:
            print(f"💰 XAUUSD+ 價格: {tick['ask']}")
            
            # 自動偵測支援的filling mode
            import MetaTrader5 as mt5
            symbol_info = mt5.symbol_info("XAUUSD+")
            filling_modes = []
            if symbol_info is not None:
                if hasattr(symbol_info, 'filling_modes') and symbol_info.filling_modes:
                    filling_modes = list(symbol_info.filling_modes)
                elif hasattr(symbol_info, 'filling_mode'):
                    filling_modes = [symbol_info.filling_mode]
            # 預設優先順序
            preferred_modes = [1, 2, 0]  # IOC, RETURN, FOK
            filling_mode = 0
            for mode in preferred_modes:
                if mode in filling_modes:
                    filling_mode = mode
                    break
            print(f"🔍 偵測到支援的filling mode: {filling_modes}，本次下單使用: {filling_mode}")
            
            # 嘗試最小下單量
            result = mt5_client.place_order(
                symbol="XAUUSD+",
                order_type=0,  # Buy
                volume=0.01,   # 最小下單量
                comment="Minimal Test",
                type_filling=filling_mode
            )
            
            if result:
                print("🎉 MT5 最小下單成功！")
                print(f"   訂單號: {result.get('order', 'N/A')}")
                
                # 等待一下
                time.sleep(2)
                
                # 查詢持倉
                positions = mt5_client.get_positions("XAUUSD+")
                if positions:
                    print(f"✅ 找到 {len(positions)} 個持倉")
                    for pos in positions:
                        print(f"   持倉號: {pos.get('ticket', 'N/A')}")
                        print(f"   數量: {pos.get('volume', 0)}")
                        
                        # 嘗試平倉
                        close_result = mt5_client.close_position(
                            pos.get('ticket', 0),
                            pos.get('volume', 0),
                            pos.get('type', 0) == 0
                        )
                        
                        if close_result:
                            print("🎉 平倉成功！")
                        else:
                            print("❌ 平倉失敗")
                else:
                    print("ℹ️ 沒有找到持倉")
            else:
                print("❌ MT5 最小下單失敗")
                print("💡 請確保 MT5 主工具列的「自動交易」按鈕是綠色")
        else:
            print("❌ 無法獲取 MT5 價格")
    else:
        print("❌ MT5 連接失敗")
    
    print("\n" + "="*40)
    print("測試完成")
    print("="*40)

if __name__ == "__main__":
    test_minimal_trading() 