#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bybit 永續合約交易客戶端
------------------------
處理 Bybit 永續合約的下單、平倉、查詢等功能
"""

import os
import time
import hmac
import hashlib
import requests
import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv
from urllib.parse import urlencode

load_dotenv('config.env')

class BybitFuturesClient:
    def __init__(self):
        self.api_key = os.getenv('BYBIT_API_KEY')
        self.api_secret = os.getenv('BYBIT_API_SECRET')
        self.base_url = "https://api.bybit.com"
        self.session = requests.Session()
        
    def _generate_signature(self, timestamp: str, recv_window: str, params: str) -> str:
        """生成 API 簽名"""
        param_str = str(timestamp) + self.api_key + recv_window + params
        hash = hmac.new(
            bytes(self.api_secret, "utf-8"),
            param_str.encode("utf-8"),
            hashlib.sha256
        )
        return hash.hexdigest()
    
    def _get_server_time(self):
        """獲取 Bybit 伺服器時間（毫秒）"""
        try:
            import requests
            resp = requests.get('https://api.bybit.com/v5/market/time', timeout=2)
            if resp.ok:
                return int(resp.json()['time'])
        except Exception as e:
            print(f"獲取 Bybit 伺服器時間失敗: {e}")
        return int(time.time() * 1000)

    def _make_request(self, method, endpoint, params=None, signed=False):
        import requests, json, hashlib, hmac, time
        url = self.base_url + endpoint
        headers = {"Content-Type": "application/json"}
        params = params or {}
        recv_window = 10000
        
        if signed:
            # 使用伺服器時間而不是本地時間
            timestamp = str(self._get_server_time())

            if method == "GET":
                # GET 請求：按照原始順序生成query_string，最後添加recvWindow
                query_string = "&".join([f"{k}={v}" for k, v in params.items()])
                query_string += f"&recvWindow={recv_window}"
                origin_string = timestamp + self.api_key + str(recv_window) + query_string
                # 添加recvWindow到params用於URL請求
                params["recvWindow"] = recv_window
            else:
                # POST 請求：在body中添加recvWindow
                params["recvWindow"] = recv_window
                body = json.dumps(params, separators=(",", ":"), ensure_ascii=False)
                origin_string = timestamp + self.api_key + str(recv_window) + body

            signature = hmac.new(self.api_secret.encode(), origin_string.encode(), hashlib.sha256).hexdigest()
            headers.update({
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-TIMESTAMP": timestamp,
                "X-BAPI-SIGN": signature,
                "X-BAPI-RECV-WINDOW": str(recv_window)
            })
            
            print(f"[DEBUG] endpoint: {endpoint}")
            print(f"[DEBUG] method: {method}")
            print(f"[DEBUG] timestamp: {timestamp}")
            print(f"[DEBUG] origin_string: {origin_string}")
            print(f"[DEBUG] signature: {signature}")
            print(f"[DEBUG] headers: {headers}")
            if method == "POST":
                print(f"[DEBUG] body: {json.dumps(params, separators=(',', ':'), ensure_ascii=False)}")
        
        try:
            if method == "GET":
                resp = requests.get(url, headers=headers, params=params, timeout=10)
            else:
                body = json.dumps(params, separators=(",", ":"), ensure_ascii=False)
                resp = requests.post(url, headers=headers, data=body, timeout=10)
            return resp.json()
        except Exception as e:
            print(f"API請求失敗: {e}")
            return {"retCode": -1, "retMsg": str(e)}
    
    def get_account_info(self) -> Optional[Dict]:
        """獲取帳戶資訊"""
        endpoint = "/v5/account/wallet-balance"
        params = {"accountType": "UNIFIED"}
        
        result = self._make_request("GET", endpoint, params, signed=True)
        
        if result.get("retCode") == 0:
            return result.get("result", {})
        else:
            print(f"獲取帳戶資訊失敗: {result}")
            return None
    
    def get_position_info(self, symbol: str = "XAUTUSDT") -> Optional[Dict]:
        """獲取持倉資訊"""
        endpoint = "/v5/position/list"
        params = {
            "category": "linear",
            "symbol": symbol
        }
        result = self._make_request("GET", endpoint, params, signed=True)
        if result.get("retCode") == 0:
            positions = result.get("result", {}).get("list", [])
            for position in positions:
                if position.get("symbol") == symbol:
                    return position
            return None
        else:
            print(f"獲取持倉資訊失敗: {result}")
            return None
    
    def get_funding_rate(self, symbol: str = "XAUTUSDT") -> Optional[float]:
        """獲取資金費率"""
        endpoint = "/v5/market/funding/history"
        params = {
            "category": "linear",
            "symbol": symbol,
            "limit": 1
        }
        result = self._make_request("GET", endpoint, params, signed=False)
        if result.get("retCode") == 0:
            funding_list = result.get("result", {}).get("list", [])
            if funding_list:
                return float(funding_list[0].get("fundingRate", 0))
        return None
    
    # 移除 set_leverage 方法，永續合約不需要額外設置槓桿

    def place_order(self, symbol: str, side: str, qty: float, order_type: str = "Market", time_in_force: str = "GTC") -> dict:
        """下單（市價單，category=linear）"""
        # 先檢查帳戶餘額
        account_info = self.get_account_info()
        if not account_info:
            print("❌ 無法獲取帳戶資訊")
            return None
            
        wallet_list = account_info.get("list", [])
        if not wallet_list:
            print("❌ 帳戶資訊格式錯誤")
            return None
            
        wallet = wallet_list[0]
        available_balance = float(wallet.get("totalAvailableBalance", 0))
        total_balance = float(wallet.get("totalWalletBalance", 0))
        
        print(f"💰 帳戶餘額檢查 - 總餘額: {total_balance:.2f} USDT, 可用餘額: {available_balance:.2f} USDT")
        
        # 計算所需保證金（使用更保守的 10% 保證金率）
        required_margin = float(qty) * 3315 * 0.10  # 10% 保證金率
        print(f"💳 所需保證金: {required_margin:.2f} USDT (10% 保證金率)")
        
        if available_balance < required_margin:
            print(f"❌ 餘額不足！需要 {required_margin:.2f} USDT，可用 {available_balance:.2f} USDT")
            print(f"💡 建議：充值到至少 {required_margin + 5:.2f} USDT 或減少下單量")
            return None
        else:
            print(f"✅ 餘額充足！可用 {available_balance:.2f} USDT，需要 {required_margin:.2f} USDT")
        
        endpoint = "/v5/order/create"
        # 強制 qty 只保留3位小數並轉成字串
        qty = str(round(float(qty), 3))
        params = {
            "category": "linear",
            "symbol": symbol,
            "side": side,  # "Buy" or "Sell"
            "orderType": order_type,
            "qty": qty,
            "timeInForce": time_in_force
        }
        # 市價單不需要 price 參數
        result = self._make_request("POST", endpoint, params, signed=True)
        if result.get("retCode") == 0:
            print(f"✅ Bybit 下單成功！訂單號: {result.get('result', {}).get('orderId', 'N/A')}")
            return result.get("result", {})
        else:
            print(f"❌ Bybit 下單失敗: {result}")
            return None
    
    def close_position(self, symbol: str, side: str, qty: float) -> Optional[Dict]:
        """平倉"""
        return self.place_order(symbol, side, qty, "Market")
    
    def get_order_history(self, symbol: str = "XAUTUSDT", limit: int = 10) -> List[Dict]:
        """獲取訂單歷史"""
        endpoint = "/v5/order/history"
        params = {
            "category": "linear",
            "symbol": symbol,
            "limit": limit
        }
        result = self._make_request("GET", endpoint, params, signed=True)
        if result.get("retCode") == 0:
            return result.get("result", {}).get("list", [])
        else:
            print(f"獲取訂單歷史失敗: {result}")
            return []
    
    def get_ticker_price(self, symbol: str = "XAUTUSDT") -> Optional[float]:
        """獲取最新價格"""
        endpoint = "/v5/market/tickers"
        params = {
            "category": "linear",
            "symbol": symbol
        }
        result = self._make_request("GET", endpoint, params, signed=False)
        if result.get("retCode") == 0:
            tickers = result.get("result", {}).get("list", [])
            if tickers:
                return float(tickers[0].get("lastPrice", 0))
        return None
    
    def calculate_position_size(self, account_balance: float, leverage: int = 10, price: float = None) -> float:
        """計算持倉大小（以 USDT 為單位）"""
        if price is None:
            price = self.get_ticker_price("XAUTUSDT")
            if price is None:
                return 0
        
        # 使用 10% 的帳戶餘額
        available_balance = account_balance * 0.1
        position_value = available_balance * leverage
        position_size = position_value / price
        
        return round(position_size, 3)
    
    def test_connection(self) -> bool:
        """測試連接"""
        print("🔍 測試 Bybit 永續合約連接...")
        
        # 測試帳戶資訊
        account_info = self.get_account_info()
        if account_info:
            print("✅ 帳戶資訊獲取成功")
            wallet_list = account_info.get("list", [])
            if wallet_list:
                wallet = wallet_list[0]
                total_balance = float(wallet.get("totalWalletBalance", 0))
                available_balance = float(wallet.get("totalAvailableBalance", 0))
                print(f"   總餘額: {total_balance:.2f} USDT")
                print(f"   可用餘額: {available_balance:.2f} USDT")
        
        # 測試價格獲取
        price = self.get_ticker_price("XAUTUSDT")
        if price:
            print(f"✅ XAUTUSDT 價格獲取成功: {price:.2f}")
        
        # 測試資金費率
        funding_rate = self.get_funding_rate("XAUTUSDT")
        if funding_rate is not None:
            print(f"✅ 資金費率獲取成功: {funding_rate:.6f}")
        
        # 測試持倉資訊
        position = self.get_position_info("XAUTUSDT")
        if position:
            print("✅ 持倉資訊獲取成功")
            size = float(position.get("size", 0))
            side = position.get("side", "")
            print(f"   持倉大小: {size}")
            print(f"   持倉方向: {side}")
        else:
            print("ℹ️ 目前無 XAUTUSDT 持倉")
        
        return True

# 測試函數
if __name__ == "__main__":
    client = BybitFuturesClient()
    client.test_connection() 