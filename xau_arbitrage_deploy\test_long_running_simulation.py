#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試長期運行的模擬交易系統
"""

import os
import sys
import time
import json
from datetime import datetime
from dotenv import load_dotenv

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
load_dotenv('config.env')

from simulation_trader import XAUSimulationTrader

def test_long_running_simulation():
    """測試長期運行的模擬系統"""
    print("🧪 測試長期運行模擬交易系統")
    print("=" * 60)
    
    # 創建模擬器
    simulator = XAUSimulationTrader()
    
    print("📊 初始狀態檢查:")
    print(f"   Bybit帳戶: ${simulator.bybit_account.balance:,.2f}")
    print(f"   MT5帳戶: ${simulator.mt5_account.balance:,.2f}")
    print(f"   最大倉位: {simulator.max_positions}")
    print(f"   進場閾值: {simulator.min_spread_threshold}%")
    print(f"   平倉閾值: {simulator.close_spread_threshold}%")
    
    # 測試市場時間檢查
    print(f"\n🕐 市場狀態檢查:")
    market_open = simulator.is_market_open()
    print(f"   當前市場狀態: {'開放' if market_open else '關閉'}")
    
    # 測試價格獲取
    print(f"\n💰 價格獲取測試:")
    prices = simulator.get_real_prices()
    if prices:
        print(f"   ✅ Bybit: ${prices['bybit']:.2f}")
        print(f"   ✅ MT5: ${prices['mt5']:.2f}")
        
        spread = simulator.calculate_spread(prices['bybit'], prices['mt5'])
        print(f"   📈 價差: {spread['percentage']:.3f}% ({spread['direction']})")
        
        # 測試倉位計算
        position_sizes = simulator.calculate_position_sizes(prices)
        if position_sizes:
            print(f"   📊 倉位計算成功")
            print(f"      Bybit: {position_sizes['bybit_qty']} 張")
            print(f"      MT5: {position_sizes['mt5_qty']} 手")
            print(f"      總保證金: ${position_sizes['total_margin']:.2f}")
        else:
            print(f"   ❌ 倉位計算失敗")
    else:
        print(f"   ❌ 價格獲取失敗")
    
    # 運行短期測試（10分鐘）
    print(f"\n🚀 開始10分鐘測試運行...")
    print("=" * 60)
    
    try:
        # 運行10分鐘的模擬
        simulator.run_simulation(duration_minutes=10, check_interval=60)
        
        # 檢查結果
        print(f"\n📊 測試完成報告:")
        summary = simulator.get_performance_summary()
        
        if summary:
            print(f"   總交易次數: {summary['total_trades']}")
            print(f"   活躍倉位: {summary['active_trades']}")
            print(f"   當前淨值: ${summary['current_equity']:,.2f}")
            print(f"   總收益: ${summary['total_return_usd']:+,.2f}")
            print(f"   收益率: {summary['total_return_pct']:+.2f}%")
            
            # 檢查帳戶狀態
            bybit_info = summary['bybit_account']
            mt5_info = summary['mt5_account']
            
            print(f"\n   Bybit帳戶:")
            print(f"      餘額: ${bybit_info['balance']:,.2f}")
            print(f"      淨值: ${bybit_info['equity']:,.2f}")
            print(f"      保證金使用: ${bybit_info['margin_used']:,.2f}")
            
            print(f"\n   MT5帳戶:")
            print(f"      餘額: ${mt5_info['balance']:,.2f}")
            print(f"      淨值: ${mt5_info['equity']:,.2f}")
            print(f"      保證金使用: ${mt5_info['margin_used']:,.2f}")
        
        # 檢查數據文件
        print(f"\n📁 數據文件檢查:")
        if os.path.exists('data/simulation_results.json'):
            with open('data/simulation_results.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"   ✅ 模擬數據文件存在")
                print(f"   📊 記錄的交易數: {data.get('simulation_info', {}).get('total_trades', 0)}")
        else:
            print(f"   ❌ 模擬數據文件不存在")
        
        if os.path.exists('simulation_trader.log'):
            print(f"   ✅ 日誌文件存在")
        else:
            print(f"   ❌ 日誌文件不存在")
        
        print(f"\n✅ 長期運行測試完成！")
        print(f"💡 系統已準備好進行一週以上的連續運行")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

def test_stability():
    """測試系統穩定性"""
    print(f"\n🔧 系統穩定性測試:")
    
    simulator = XAUSimulationTrader()
    
    # 測試多次價格獲取
    success_count = 0
    total_tests = 10
    
    for i in range(total_tests):
        prices = simulator.get_real_prices()
        if prices:
            success_count += 1
        time.sleep(2)  # 等待2秒
    
    success_rate = (success_count / total_tests) * 100
    print(f"   價格獲取成功率: {success_rate:.1f}% ({success_count}/{total_tests})")
    
    if success_rate >= 80:
        print(f"   ✅ 系統穩定性良好")
    else:
        print(f"   ⚠️ 系統穩定性需要改善")

if __name__ == "__main__":
    test_long_running_simulation()
    test_stability()
