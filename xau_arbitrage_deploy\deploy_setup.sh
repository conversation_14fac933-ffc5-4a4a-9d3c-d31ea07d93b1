#!/bin/bash
# XAU套利模擬交易系統 - 雲端部署腳本

echo "🚀 XAU套利模擬交易系統 - 雲端部署設置"
echo "=================================================="

# 更新系統
echo "📦 更新系統套件..."
sudo apt update && sudo apt upgrade -y

# 安裝Python3和pip
echo "🐍 安裝Python3和相關工具..."
sudo apt install -y python3 python3-pip python3-venv python3-dev

# 安裝系統依賴
echo "🔧 安裝系統依賴..."
sudo apt install -y build-essential libssl-dev libffi-dev

# 進入項目目錄
cd ~/xau_arbitrage

# 創建虛擬環境
echo "🌐 創建Python虛擬環境..."
python3 -m venv venv

# 激活虛擬環境
echo "⚡ 激活虛擬環境..."
source venv/bin/activate

# 安裝Python依賴
echo "📚 安裝Python依賴套件..."
pip install --upgrade pip
pip install -r requirements.txt

# 創建數據目錄
echo "📁 創建數據目錄..."
mkdir -p data
mkdir -p logs

# 設置權限
echo "🔐 設置文件權限..."
chmod +x cloud_simulation_runner.py
chmod +x deploy_setup.sh

# 創建systemd服務文件
echo "⚙️ 創建系統服務..."
sudo tee /etc/systemd/system/xau-simulation.service > /dev/null <<EOF
[Unit]
Description=XAU Arbitrage Simulation Trading System
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/xau_arbitrage
Environment=PATH=/home/<USER>/xau_arbitrage/venv/bin
ExecStart=/home/<USER>/xau_arbitrage/venv/bin/python /home/<USER>/xau_arbitrage/cloud_simulation_runner.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 重新載入systemd
echo "🔄 重新載入systemd..."
sudo systemctl daemon-reload

# 啟用服務
echo "✅ 啟用XAU模擬交易服務..."
sudo systemctl enable xau-simulation

echo ""
echo "🎉 部署設置完成！"
echo "=================================================="
echo "📋 接下來的步驟："
echo "1. 檢查config.env文件是否正確設置"
echo "2. 測試運行: python3 cloud_simulation_runner.py"
echo "3. 啟動服務: sudo systemctl start xau-simulation"
echo "4. 查看狀態: sudo systemctl status xau-simulation"
echo "5. 查看日誌: sudo journalctl -u xau-simulation -f"
echo "=================================================="
