#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XAU 套利交易系統
---------------
整合監控、交易執行和通知功能的完整套利系統
"""

import os
import time
import uuid
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv

from bybit_futures_client import BybitFuturesClient
from cloud_config import get_mt5_client
from telegram_notifier import TelegramNotifier

load_dotenv('config.env')

class XAUArbitrageTrader:
    def __init__(self):
        # 初始化客户端（自动选择云端兼容版本）
        self.bybit_client = BybitFuturesClient()
        MT5ClientClass = get_mt5_client()
        self.mt5_client = MT5ClientClass()
        self.notifier = TelegramNotifier()

        # 交易設定 - 根據新需求調整
        self.min_spread_threshold = 0.2  # 進場價差閾值 0.2%
        self.close_spread_threshold = 0.05  # 平倉價差閾值 0.05%
        self.risk_spread_threshold = 0.5  # 風控價差閾值 0.5%
        self.leverage = 20  # 槓桿倍數 20倍
        self.position_size_ratio = 0.1  # 每次使用帳戶餘額的10%
        self.max_positions = 5  # 最多同時持有5個套利倉位

        # 交易狀態
        self.active_trades = {}  # 活躍交易記錄 - 強制重置為空
        self.trade_history = []  # 交易歷史
        self.risk_mode = False  # 風控模式
        self.risk_mode_start_time = None  # 風控模式開始時間
        self.mt5_market_open = True  # MT5市場開放狀態
        
        # 🚨 緊急停止模式 - 立即停止所有新交易
        self.emergency_stop = False  # 關閉緊急停止
        self.emergency_stop_reason = "名義價值計算錯誤，需要修正"
        
        # 數據檔案
        self.trades_file = 'data/arbitrage_trades.json'
        self.csv_file = 'xau_spread_data.csv'
        self.log_file = 'xau_arbitrage_trader.log'
        self.ensure_data_directory()
        
        # 初始化日誌
        self.setup_logging()
        
        # 初始化 CSV 檔案
        self.init_csv_file()

        # 🔍 啟動時檢測實際倉位狀況
        self.check_startup_positions()
        
    def ensure_data_directory(self):
        """確保數據目錄存在"""
        os.makedirs('data', exist_ok=True)
    
    def setup_logging(self):
        """設置日誌記錄"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def init_csv_file(self):
        """初始化 CSV 檔案"""
        if not os.path.exists(self.csv_file):
            df = pd.DataFrame(columns=[
                'timestamp', 'bybit_xautusdt', 'mt5_xauusd',
                'absolute_spread', 'percentage_spread', 'direction',
                'funding_rate', 'trade_triggered'
            ])
            df.to_csv(self.csv_file, index=False, encoding='utf-8')
            self.logger.info(f"初始化 CSV 檔案: {self.csv_file}")

    def check_startup_positions(self):
        """啟動時檢查倉位狀況"""
        print("🔍 檢測當前實際倉位狀況...")

        try:
            actual_positions = self.detect_existing_positions()

            if actual_positions['has_positions']:
                print(f"⚠️ 檢測到現有倉位:")
                print(f"   Bybit: {actual_positions['bybit_positions']} 個倉位")
                print(f"   MT5: {actual_positions['mt5_positions']} 個倉位")

                if actual_positions['details']:
                    print("   詳細信息:")
                    for detail in actual_positions['details']:
                        print(f"     - {detail}")

                print(f"   ⚠️ 建議: 請先手動平倉所有倉位，然後重新啟動系統")
                print(f"   🔄 活躍倉位計數器已重置為 0（實際倉位仍存在）")

                # 重置活躍倉位計數器
                self.active_trades = {}
            else:
                print("✅ 未檢測到現有倉位，系統可以正常啟動")
                self.active_trades = {}

        except Exception as e:
            print(f"⚠️ 檢測倉位時發生錯誤: {e}")
            print("🔄 活躍倉位計數器已重置為 0")
            self.active_trades = {}

        # 載入歷史數據
        self.load_trade_data()
    
    def log_spread_data(self, prices: Dict, spread_info: Dict, funding_rate: float, trade_triggered: bool = False):
        """記錄價差數據到 CSV"""
        try:
            timestamp = datetime.now()
            new_row = {
                'timestamp': timestamp,
                'bybit_xautusdt': prices.get('bybit', 0),
                'mt5_xauusd': prices.get('mt5', 0),
                'absolute_spread': spread_info['absolute'],
                'percentage_spread': spread_info['percentage'],
                'direction': spread_info['direction'],
                'funding_rate': funding_rate,
                'trade_triggered': trade_triggered
            }
            
            df = pd.read_csv(self.csv_file)
            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
            df.to_csv(self.csv_file, index=False, encoding='utf-8')
            
        except Exception as e:
            self.logger.error(f"記錄 CSV 數據失敗: {e}")
        
    def get_current_prices(self) -> Dict[str, float]:
        """獲取當前價格"""
        prices = {}
        
        # 獲取 Bybit 永續合約價格
        bybit_price = self.bybit_client.get_ticker_price("XAUTUSDT")
        if bybit_price:
            prices['bybit'] = bybit_price
        else:
            print("⚠️ 無法獲取 Bybit XAUTUSDT 價格")
        
        # 獲取 MT5 現貨價格
        if self.mt5_client.connect():
            tick = self.mt5_client.get_tick("XAUUSD+")
            self.mt5_client.disconnect()
            if tick:
                prices['mt5'] = (tick['bid'] + tick['ask']) / 2
            else:
                print("⚠️ 無法獲取 MT5 XAUUSD+ 價格")
        else:
            print("⚠️ MT5 連接失敗")
        
        return prices
    
    def calculate_spread(self, bybit_price: float, mt5_price: float) -> Dict[str, float]:
        """計算價差"""
        absolute_spread = mt5_price - bybit_price
        percentage_spread = (absolute_spread / bybit_price) * 100
        
        return {
            'absolute': absolute_spread,
            'percentage': percentage_spread,
            'direction': 'MT5 > Bybit' if percentage_spread > 0 else 'Bybit > MT5'
        }
    


    def check_arbitrage_conditions(self, spread_info: Dict, funding_rate: float) -> bool:
        """檢查套利條件 - 使用固定阈值 0.2%进场"""
        # 🚨 緊急停止檢查
        if self.emergency_stop:
            self.logger.warning(f"🚨 緊急停止模式: {self.emergency_stop_reason}")
            return False
            
        # 檢查是否在風控模式
        if self.is_risk_mode_active():
            return False

        # 檢查MT5市場是否開放
        if not self.check_mt5_market_status():
            return False

        # 檢查是否已達最大倉位數
        if len(self.active_trades) >= self.max_positions:
            return False

        # 只檢查價差是否大於等於進場閾值 0.2%
        if abs(spread_info['percentage']) < self.min_spread_threshold:
            return False

        # 不檢查資金費率，直接進場
        return True

    def check_risk_control(self, spread_info: Dict) -> bool:
        """檢查風控條件"""
        return abs(spread_info['percentage']) >= self.risk_spread_threshold

    def check_close_conditions(self, spread_info: Dict, entry_strategy: str = None) -> bool:
        """檢查平倉條件 - 使用固定阈值 0.05%出场"""
        return abs(spread_info['percentage']) <= self.close_spread_threshold

    def is_risk_mode_active(self) -> bool:
        """檢查風控模式是否仍在激活狀態"""
        if not self.risk_mode:
            return False

        if self.risk_mode_start_time is None:
            return False

        # 檢查是否已過24小時
        current_time = datetime.now()
        time_diff = current_time - self.risk_mode_start_time
        if time_diff.total_seconds() >= 24 * 3600:  # 24小時
            self.risk_mode = False
            self.risk_mode_start_time = None
            self.logger.info("風控模式已解除")
            return False

        return True

    def check_weekend_status(self) -> bool:
        """檢查市場是否開放 - 基於Bybit MT5實際交易時間(東八區 UTC+8)"""
        import pytz

        # 獲取東八區時間 (本地時間)
        utc8_tz = pytz.timezone('Asia/Shanghai')  # UTC+8
        now_utc8 = datetime.now(utc8_tz)
        weekday = now_utc8.weekday()  # 0=Monday, 6=Sunday
        hour = now_utc8.hour
        minute = now_utc8.minute
        current_time = hour * 60 + minute  # 转换为分钟

        # 根据Bybit MT5交易时间表 (转换为东八区时间)
        # 原GMT+3时间转换为UTC+8 (加5小时)
        market_open = False

        if weekday == 6:  # Sunday
            market_open = False  # No trading
        elif weekday == 5:  # Saturday
            # 原00:00-04:57 GMT+3 = 05:00-09:57 UTC+8
            market_open = (5 * 60) <= current_time <= (9 * 60 + 57)
        else:  # Monday-Friday
            # 原06:00-23:59 GMT+3 = 11:00-04:59+1 UTC+8
            # 原00:00-04:58 GMT+3 = 05:00-09:58 UTC+8
            market_open = (current_time >= 11 * 60) or (current_time <= (9 * 60 + 58))

        if not market_open:
            weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            self.logger.info(f"市场休市检测 - 东八区时间: {now_utc8.strftime('%Y-%m-%d %H:%M:%S')}, {weekday_names[weekday]}, 市场关闭")

        return market_open

    def check_mt5_trading_time(self) -> bool:
        """根據東八區(UTC+8)的MT5交易時段判斷是否可交易"""
        import pytz
        utc8_tz = pytz.timezone('Asia/Shanghai')
        now = datetime.now(utc8_tz)
        weekday = now.weekday()  # 0=Monday, 6=Sunday
        hour = now.hour
        minute = now.minute
        t = hour * 60 + minute

        # 週一
        if weekday == 0:
            return (hour > 6 or (hour == 6 and minute >= 1)) and (hour < 24)
        # 週二～週五
        elif weekday in [1,2,3,4]:
            # 00:00-04:58、06:01-23:59
            if (0 <= t <= 4*60+58) or (6*60+1 <= t <= 23*60+59):
                return True
            else:
                return False
        # 週六
        elif weekday == 5:
            # 00:00-04:57
            return 0 <= t <= 4*60+57
        # 週日
        else:
            return False

    def check_mt5_market_status(self) -> bool:
        """檢查MT5市場是否開放（依據精確時段）"""
        # 先檢查本地交易時段
        if not self.check_mt5_trading_time():
            if hasattr(self, 'mt5_market_open') and self.mt5_market_open:
                self.mt5_market_open = False
                message = f"⏰ MT5本地交易時段關閉 - 套利暫停"
                self.logger.info(message)
                self.notifier.send_message(message)
            return False
        # 連接MT5確認trade_mode
        try:
            if not self.mt5_client.connect():
                self.logger.warning("無法連接MT5檢查市場狀態")
                return False
            symbol_info = self.mt5_client.get_symbol_info("XAUUSD+")
            self.mt5_client.disconnect()
            if not symbol_info:
                return False
            trade_mode = symbol_info.get('trade_mode', 0)
            market_open = trade_mode in [1, 2, 4]
            self.logger.info(f"MT5市場檢測 - trade_mode: {trade_mode}, 市場開放: {market_open}")
            if market_open != self.mt5_market_open:
                self.mt5_market_open = market_open
                status_msg = "開放" if market_open else "關閉"
                message = f"📊 MT5 XAUUSD+ 市場狀態變更: {status_msg}"
                self.logger.info(message)
                self.notifier.send_message(message)
            return market_open
        except Exception as e:
            self.logger.error(f"檢查MT5市場狀態錯誤: {e}")
            return False
    
    def calculate_position_sizes(self, bybit_price: float, mt5_price: float) -> Dict[str, float]:
        """計算持倉大小 - 確保兩邊名義價值完全相等"""
        try:
            # 獲取 Bybit 帳戶餘額
            bybit_account = self.bybit_client.get_account_info()
            bybit_balance = 0
            if bybit_account and bybit_account.get("list"):
                bybit_balance = float(bybit_account["list"][0].get("totalAvailableBalance", 0))
            
            # 獲取 MT5 帳戶餘額
            mt5_balance = 0
            if self.mt5_client.connect():
                account_info = self.mt5_client.get_account_info()
                self.mt5_client.disconnect()
                if account_info:
                    mt5_balance = float(account_info.get("balance", 0))
            
            self.logger.info(f"持倉計算 - Bybit餘額: {bybit_balance:.2f}, MT5餘額: {mt5_balance:.2f}")
            
            # 使用較小的餘額作為基準
            min_balance = min(bybit_balance, mt5_balance) if bybit_balance > 0 and mt5_balance > 0 else 10
            
            # 計算單邊保證金（使用帳戶餘額的10%）
            margin_per_side = min_balance * self.position_size_ratio
            self.logger.info(f"使用較小餘額: {min_balance:.2f}, 單邊保證金: {margin_per_side:.2f}")
            
            # 計算目標名義價值
            target_nominal_value = max(5.0, margin_per_side * self.leverage)
            self.logger.info(f"目標名義價值: {target_nominal_value:.2f}, 槓桿: {self.leverage}x")
            
            # 根據MT5保證金要求調整下單量
            # MT5黃金通常需要1-5%保證金，我們使用保守的5%
            mt5_margin_ratio = 0.05  # 5%保證金率
            mt5_max_position_value = mt5_balance / mt5_margin_ratio
            
            # 確保不超過MT5最大可開倉價值
            actual_nominal_value = min(target_nominal_value, mt5_max_position_value)

            # 🚨 重要修正：正確理解合約規格差異
            # Bybit XAUTUSDT: 1張 = 1盎司黃金
            # MT5 XAUUSD+: 1手 = 100盎司黃金
            #
            # 問題：之前的邏輯錯誤地認為兩邊的0.01單位是等價的
            # 實際上：MT5的0.01手 = 1盎司 = Bybit的1張

            # 定義最小下單量
            mt5_min_qty = 0.01     # MT5最小0.01手 = 1盎司
            bybit_min_qty = 0.001  # Bybit最小0.001張 = 0.001盎司

            # 🚨 關鍵：計算等價的黃金盎司數，而不是名義價值
            
            # 🚨 簡化邏輯：直接以最小交易單位為基準
            #
            # 關鍵理解：
            # - Bybit: 1張 = 1盎司黃金
            # - MT5: 1手 = 100盎司黃金
            # - 所以 MT5的0.01手 = 1盎司 = Bybit的1張

            # 🎯 目標：讓兩邊交易相同盎司數的黃金

            # 方案：使用MT5的最小單位作為基準
            # MT5最小：0.01手 = 1盎司
            # Bybit對應：1張 = 1盎司

            # 但考慮到資金限制，我們需要計算合適的倍數
            mt5_base_oz = 1.0  # MT5的0.01手對應1盎司

            # 計算可以交易多少個基準單位
            max_units_by_funds = actual_nominal_value / (mt5_base_oz * mt5_price)

            # 至少交易1個基準單位
            trade_units = max(1, int(max_units_by_funds))

            # 計算最終盎司數
            target_oz = trade_units * mt5_base_oz

            # 計算對應的下單量
            mt5_qty = target_oz / 100    # 盎司數 ÷ 100盎司/手
            bybit_qty = target_oz        # 盎司數 = 張數

            # 確保滿足最小下單量
            mt5_qty = max(mt5_min_qty, mt5_qty)
            bybit_qty = max(bybit_min_qty, bybit_qty)

            # 🚨 關鍵：重新同步盎司數
            # 如果調整後的數量不匹配，以較小的為準
            actual_mt5_oz = mt5_qty * 100
            actual_bybit_oz = bybit_qty

            self.logger.info(f"🔍 同步前 - MT5: {mt5_qty}手={actual_mt5_oz}oz, Bybit: {bybit_qty}張={actual_bybit_oz}oz")

            if abs(actual_mt5_oz - actual_bybit_oz) > 0.001:
                # 同步到較小的盎司數
                sync_oz = min(actual_mt5_oz, actual_bybit_oz)
                mt5_qty = sync_oz / 100
                bybit_qty = sync_oz

                self.logger.info(f"🔄 同步到 {sync_oz}oz - MT5: {mt5_qty}手, Bybit: {bybit_qty}張")

                # 再次確保最小下單量
                mt5_qty = max(mt5_min_qty, mt5_qty)
                bybit_qty = max(bybit_min_qty, bybit_qty)

                self.logger.info(f"🔄 最小量調整後 - MT5: {mt5_qty}手, Bybit: {bybit_qty}張")
            
            # 計算最終名義價值
            final_bybit_nominal = bybit_qty * bybit_price      # 張數 × 價格
            final_mt5_nominal = mt5_qty * 100 * mt5_price      # 手數 × 100盎司/手 × 價格

            # 計算最終盎司數（用於驗證）
            final_bybit_oz = bybit_qty
            final_mt5_oz = mt5_qty * 100
            
            # 🚨 重要：檢查是否超過資金限制
            # 使用較小的名義價值來判斷（因為兩邊價值應該相近）
            min_nominal = min(final_bybit_nominal, final_mt5_nominal)

            self.logger.info(f"🔍 資金檢查 - 最小名義價值: ${min_nominal:.2f}, 限制: ${actual_nominal_value:.2f}")

            if min_nominal > actual_nominal_value:
                self.logger.info("⚠️ 超過資金限制，需要縮放")
                # 計算需要的盎司數縮放比例
                scale_factor = actual_nominal_value / min_nominal
                target_oz_scaled = final_bybit_oz * scale_factor

                self.logger.info(f"🔄 縮放因子: {scale_factor:.3f}, 目標盎司數: {target_oz_scaled:.3f}")

                # 重新計算下單量（保持盎司數匹配）
                bybit_qty = target_oz_scaled
                mt5_qty = target_oz_scaled / 100

                self.logger.info(f"🔄 縮放計算 - MT5: {mt5_qty}手, Bybit: {bybit_qty}張")

                # 🚨 關鍵：如果MT5數量小於最小值，需要重新調整
                if mt5_qty < mt5_min_qty:
                    self.logger.info(f"⚠️ MT5數量{mt5_qty}小於最小值{mt5_min_qty}，重新調整")
                    # 使用MT5最小值，並同步Bybit
                    mt5_qty = mt5_min_qty
                    bybit_qty = mt5_qty * 100  # MT5手數 × 100 = 盎司數 = Bybit張數
                    self.logger.info(f"🔄 重新同步 - MT5: {mt5_qty}手, Bybit: {bybit_qty}張")

                # 確保Bybit最小下單量
                bybit_qty = max(bybit_min_qty, bybit_qty)

                self.logger.info(f"🔄 最終調整 - MT5: {mt5_qty}手, Bybit: {bybit_qty}張")

                # 重新計算最終值
                final_bybit_nominal = bybit_qty * bybit_price
                final_mt5_nominal = mt5_qty * 100 * mt5_price
                final_bybit_oz = bybit_qty
                final_mt5_oz = mt5_qty * 100
            else:
                self.logger.info("✅ 資金充足，無需縮放")

            # 📊 詳細日誌輸出
            self.logger.info(f"📊 下單數量 - Bybit: {bybit_qty:.3f}張, MT5: {mt5_qty:.2f}手")
            self.logger.info(f"🥇 黃金盎司數 - Bybit: {final_bybit_oz:.3f}oz, MT5: {final_mt5_oz:.3f}oz")
            self.logger.info(f"💰 名義價值 - Bybit: ${final_bybit_nominal:.2f}, MT5: ${final_mt5_nominal:.2f}")
            self.logger.info(f"📈 盎司數差異: {abs(final_bybit_oz - final_mt5_oz):.3f}oz")
            self.logger.info(f"📈 名義價值差異: ${abs(final_bybit_nominal - final_mt5_nominal):.2f}")

            # 驗證盎司數匹配（這是關鍵）
            oz_diff = abs(final_bybit_oz - final_mt5_oz)
            if oz_diff > 0.1:  # 允許0.1盎司的差異
                self.logger.error(f"❌ 黃金盎司數差異過大: {oz_diff:.3f}oz")
                return None
            else:
                self.logger.info(f"✅ 黃金盎司數匹配成功，差異: {oz_diff:.3f}oz")
            
            return {
                'bybit_qty': bybit_qty,
                'mt5_qty': mt5_qty,
                'margin_per_side': margin_per_side,
                'position_value': (final_bybit_nominal + final_mt5_nominal) / 2,  # 使用平均名義價值
                'bybit_nominal': final_bybit_nominal,
                'mt5_nominal': final_mt5_nominal,
                'bybit_oz': final_bybit_oz,
                'mt5_oz': final_mt5_oz
            }
            
        except Exception as e:
            self.logger.error(f"計算持倉大小失敗: {e}")
            return None
    
    def execute_arbitrage_trade(self, spread_info: Dict, prices: Dict, 
                               position_sizes: Dict) -> Optional[Dict]:
        """執行套利交易"""
        trade_id = str(uuid.uuid4())[:8]
        trade_start_time = datetime.now()
        
        print(f"🎯 開始執行套利交易 {trade_id}")
        
        # 下單前檢查MT5市場狀態
        if not self.check_mt5_market_status():
            print("❌ MT5市場未開放，取消下單")
            self.logger.warning(f"套利交易 {trade_id} 取消 - MT5市場未開放")
            return None
        
        # 決定交易方向
        if spread_info['percentage'] > 0:
            # MT5 價格較高，賣出 MT5，買入 Bybit
            mt5_side = "Sell"
            bybit_side = "Buy"
        else:
            # Bybit 價格較高，買入 MT5，賣出 Bybit
            mt5_side = "Buy"
            bybit_side = "Sell"
        
        trade_result = {
            'trade_id': trade_id,
            'start_time': trade_start_time,
            'bybit_side': bybit_side,
            'mt5_side': mt5_side,
            'bybit_qty': position_sizes['bybit_qty'],
            'mt5_qty': position_sizes['mt5_qty'],
            'bybit_price': prices['bybit'],
            'mt5_price': prices['mt5'],
            'spread': spread_info['percentage'],
            'status': 'executing',
            'entry_threshold': self.min_spread_threshold,  # 记录进场阈值
            'exit_threshold': self.close_spread_threshold   # 记录出场阈值
        }
        
        try:
            # 🚨 修正：先執行MT5下單，再執行Bybit下單，避免扣款速度不對稱
            
            # 先執行 MT5 訂單
            print(f"📊 先執行 MT5 {mt5_side} 訂單...")
            
            # 使用與主程式相同的連接方式
            if not self.mt5_client.connect():
                print("❌ MT5 連接失敗")
                return None
            
            # 🚀 準備同時下單，追求最快速度和最小點差
            print(f"🚀 準備同時下單 - MT5 {mt5_side}, Bybit {bybit_side}")

            # 準備下單參數
            mt5_order_type = 0 if mt5_side == "Buy" else 1  # 0=Buy, 1=Sell
            mt5_qty = max(0.01, position_sizes['mt5_qty'])  # 至少0.01手
            print(f"📊 下單量 - MT5: {mt5_qty} 手, Bybit: {position_sizes['bybit_qty']}")

            # 下單前檢查 MT5 狀態
            print("🔍 下單前檢查 MT5 狀態...")
            account_info = self.mt5_client.get_account_info()
            if account_info:
                print(f"💰 MT5 帳戶餘額: {account_info.get('balance', 0):.2f}")
                print(f"💰 MT5 可用保證金: {account_info.get('margin_free', 0):.2f}")

            # 檢查市場狀態
            symbol_info = self.mt5_client.get_symbol_info("XAUUSD+")
            if symbol_info:
                print(f"📈 XAUUSD+ 交易模式: {symbol_info.get('trade_mode', 0)}")

            # 初始化下單結果
            mt5_order = None
            bybit_order = None

            try:
                # ⚡ 同時執行兩邊下單
                print("⚡ 開始同時下單...")
                start_time = time.time()

                # MT5下單
                mt5_order = self.mt5_client.place_order(
                    symbol="XAUUSD+",
                    order_type=mt5_order_type,
                    volume=mt5_qty,
                    comment=f"Arbitrage {trade_id}"
                )

                # Bybit下單（幾乎同時）
                bybit_order = self.bybit_client.place_order(
                    symbol="XAUTUSDT",
                    side=bybit_side,
                    qty=position_sizes['bybit_qty'],
                    order_type="Market",
                    time_in_force="GTC"
                )

                end_time = time.time()
                print(f"⚡ 同時下單完成，耗時: {(end_time - start_time)*1000:.0f}ms")

            finally:
                # 立即斷開MT5連接
                self.mt5_client.disconnect()

            # 記錄下單結果
            trade_result['mt5_order'] = mt5_order
            trade_result['bybit_order'] = bybit_order

            # 檢查下單結果
            mt5_success = mt5_order is not None
            bybit_success = bybit_order is not None

            print(f"📊 下單結果 - MT5: {'✅成功' if mt5_success else '❌失敗'}, Bybit: {'✅成功' if bybit_success else '❌失敗'}")

            # 如果兩邊都失敗，直接返回
            if not mt5_success and not bybit_success:
                print("❌ 兩邊下單都失敗")
                return None

            # ⏱️ 等待1秒後檢測兩邊倉位是否都成功開啟
            print("⏳ 等待1秒後檢測倉位狀態...")
            time.sleep(1)

            # 🔍 檢測倉位是否成功開啟
            position_check_result = self._verify_positions_opened(trade_id, trade_result)

            # 根據檢測結果決定處理方式
            if position_check_result['success']:
                print("✅ 兩邊倉位都已成功開啟")
                trade_result['status'] = 'active'
            else:
                print(f"❌ 倉位檢測失敗: {position_check_result['message']}")

                # 檢查具體情況並處理
                bybit_has_position = position_check_result.get('bybit_position') is not None
                mt5_has_position = position_check_result.get('mt5_position') is not None

                if bybit_has_position and mt5_has_position:
                    # 兩邊都有倉位但檢測失敗（異常情況）
                    print("⚠️ 兩邊都有倉位但檢測異常，繼續執行")
                    trade_result['status'] = 'active'
                elif bybit_has_position or mt5_has_position:
                    # 只有一邊有倉位，需要平倉
                    print("🚨 檢測到單邊倉位，執行緊急平倉")
                    self._emergency_close_failed_positions(trade_id, position_check_result)
                    return None
                else:
                    # 兩邊都沒有倉位
                    print("❌ 兩邊都沒有檢測到倉位，交易失敗")
                    return None
            
            # 計算保證金和手續費
            bybit_margin = position_sizes['margin_per_side']
            mt5_margin = position_sizes['margin_per_side']
            
            # 估算手續費 (通常為 0.1%)
            bybit_fee = position_sizes['position_value'] * 0.001
            mt5_fee = position_sizes['position_value'] * 0.001
            
            trade_result.update({
                'bybit_margin': bybit_margin,
                'mt5_margin': mt5_margin,
                'bybit_fee': bybit_fee,
                'mt5_fee': mt5_fee,
                'total_margin': bybit_margin + mt5_margin
            })
            
            # 保存到活躍交易記錄
            self.active_trades[trade_id] = trade_result

            # 發送詳細的開倉通知
            self.send_opening_notification(trade_result)

            print(f"✅ 套利交易 {trade_id} 執行成功")
            self.logger.info(f"套利交易 {trade_id} 開倉成功")
            return trade_result
            
        except Exception as e:
            print(f"❌ 套利交易執行錯誤: {e}")
            self.notifier.send_error_notification(str(e), "套利交易執行")
            return None

    def _verify_positions_opened(self, trade_id: str, trade_result: Dict) -> Dict:
        """檢測兩邊倉位是否都成功開啟"""
        result = {
            'success': False,
            'message': '',
            'bybit_position': None,
            'mt5_position': None
        }

        try:
            # 檢測 Bybit 倉位
            print("🔍 檢測 Bybit 倉位...")
            bybit_position = self.bybit_client.get_position_info("XAUTUSDT")
            if bybit_position and float(bybit_position.get("size", 0)) > 0:
                result['bybit_position'] = bybit_position
                print(f"✅ Bybit 倉位確認: {bybit_position.get('size')} {bybit_position.get('side')}")
            else:
                result['message'] = "Bybit 倉位未找到或為0"
                print(f"❌ {result['message']}")
                return result

            # 檢測 MT5 倉位
            print("🔍 檢測 MT5 倉位...")
            if not self.mt5_client.connect():
                result['message'] = "無法連接MT5檢測倉位"
                print(f"❌ {result['message']}")
                return result

            mt5_positions = self.mt5_client.get_positions("XAUUSD+")
            self.mt5_client.disconnect()

            if mt5_positions and len(mt5_positions) > 0:
                # 找到最新的倉位（按ticket排序）
                latest_position = max(mt5_positions, key=lambda x: x.get('ticket', 0))
                result['mt5_position'] = latest_position
                print(f"✅ MT5 倉位確認: {latest_position.get('volume')} 手, ticket: {latest_position.get('ticket')}")
            else:
                result['message'] = "MT5 倉位未找到"
                print(f"❌ {result['message']}")
                return result

            # 兩邊倉位都確認成功
            result['success'] = True
            result['message'] = "兩邊倉位都已成功開啟"
            return result

        except Exception as e:
            result['message'] = f"倉位檢測異常: {e}"
            print(f"❌ {result['message']}")
            return result

    def _emergency_close_mt5_position(self, trade_id: str):
        """緊急平倉MT5倉位（當Bybit下單失敗時）"""
        try:
            print(f"🚨 緊急平倉MT5倉位 (交易ID: {trade_id})")

            if not self.mt5_client.connect():
                print("❌ 無法連接MT5進行緊急平倉")
                return False

            # 獲取所有XAUUSD+倉位
            positions = self.mt5_client.get_positions("XAUUSD+")
            if not positions:
                print("❌ 未找到需要平倉的MT5倉位")
                self.mt5_client.disconnect()
                return False

            # 平倉最新的倉位
            latest_position = max(positions, key=lambda x: x.get('ticket', 0))
            close_result = self.mt5_client.close_position(ticket=latest_position['ticket'])
            self.mt5_client.disconnect()

            if close_result:
                print(f"✅ MT5緊急平倉成功: ticket {latest_position['ticket']}")
                # 發送緊急平倉通知
                message = f"""🚨 緊急平倉通知

🆔 交易ID: {trade_id}
⚠️ 原因: Bybit下單失敗，MT5已成功平倉
📊 MT5倉位: {latest_position.get('volume')} 手
🎫 Ticket: {latest_position['ticket']}
⏰ 時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

✅ 已避免單邊風險暴露"""
                self.notifier.send_message(message)
                return True
            else:
                print(f"❌ MT5緊急平倉失敗: ticket {latest_position['ticket']}")
                return False

        except Exception as e:
            print(f"❌ MT5緊急平倉異常: {e}")
            return False

    def _emergency_close_failed_positions(self, trade_id: str, position_check_result: Dict):
        """緊急平倉失敗的倉位（當倉位檢測失敗時）"""
        try:
            print(f"🚨 開始緊急平倉程序 (交易ID: {trade_id})")

            closed_positions = []

            # 如果Bybit有倉位但MT5沒有，平倉Bybit
            if position_check_result.get('bybit_position') and not position_check_result.get('mt5_position'):
                print("🔄 平倉Bybit倉位...")
                bybit_pos = position_check_result['bybit_position']
                close_side = "Sell" if bybit_pos.get('side') == "Buy" else "Buy"
                close_result = self.bybit_client.close_position(
                    symbol="XAUTUSDT",
                    side=close_side,
                    qty=float(bybit_pos.get('size', 0))
                )
                if close_result:
                    closed_positions.append(f"Bybit: {bybit_pos.get('size')} {bybit_pos.get('side')}")
                    print("✅ Bybit倉位已平倉")
                else:
                    print("❌ Bybit倉位平倉失敗")

            # 如果MT5有倉位但Bybit沒有，平倉MT5
            elif position_check_result.get('mt5_position') and not position_check_result.get('bybit_position'):
                print("🔄 平倉MT5倉位...")
                if not self.mt5_client.connect():
                    print("❌ 無法連接MT5進行平倉")
                    return

                mt5_pos = position_check_result['mt5_position']
                close_result = self.mt5_client.close_position(ticket=mt5_pos['ticket'])
                self.mt5_client.disconnect()

                if close_result:
                    closed_positions.append(f"MT5: {mt5_pos.get('volume')} 手, ticket: {mt5_pos['ticket']}")
                    print("✅ MT5倉位已平倉")
                else:
                    print("❌ MT5倉位平倉失敗")

            # 🚨 新增：如果兩邊都沒有倉位，但下單可能成功了，需要檢查並平倉
            elif not position_check_result.get('bybit_position') and not position_check_result.get('mt5_position'):
                print("⚠️ 兩邊都沒有檢測到倉位，但可能存在隱藏倉位，進行深度檢查...")

                # 深度檢查Bybit倉位（可能有延遲）
                time.sleep(2)  # 再等2秒
                bybit_position = self.bybit_client.get_position_info("XAUTUSDT")
                if bybit_position and float(bybit_position.get("size", 0)) > 0:
                    print("🔍 發現延遲的Bybit倉位，立即平倉...")
                    close_side = "Sell" if bybit_position.get('side') == "Buy" else "Buy"
                    close_result = self.bybit_client.close_position(
                        symbol="XAUTUSDT",
                        side=close_side,
                        qty=float(bybit_position.get('size', 0))
                    )
                    if close_result:
                        closed_positions.append(f"Bybit(延遲檢測): {bybit_position.get('size')} {bybit_position.get('side')}")
                        print("✅ 延遲Bybit倉位已平倉")

                # 深度檢查MT5倉位
                if self.mt5_client.connect():
                    mt5_positions = self.mt5_client.get_positions("XAUUSD+")
                    if mt5_positions and len(mt5_positions) > 0:
                        print("🔍 發現延遲的MT5倉位，立即平倉...")
                        for mt5_pos in mt5_positions:
                            close_result = self.mt5_client.close_position(ticket=mt5_pos['ticket'])
                            if close_result:
                                closed_positions.append(f"MT5(延遲檢測): {mt5_pos.get('volume')} 手, ticket: {mt5_pos['ticket']}")
                                print(f"✅ 延遲MT5倉位已平倉: ticket {mt5_pos['ticket']}")
                    self.mt5_client.disconnect()

            # 發送緊急平倉通知
            if closed_positions:
                message = f"""🚨 緊急平倉通知

🆔 交易ID: {trade_id}
⚠️ 原因: {position_check_result['message']}
🔄 已平倉倉位:
{chr(10).join(['• ' + pos for pos in closed_positions])}
⏰ 時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

✅ 已避免單邊風險暴露"""
                self.notifier.send_message(message)

        except Exception as e:
            print(f"❌ 緊急平倉程序異常: {e}")
            # 發送緊急錯誤通知
            error_message = f"""🚨 緊急平倉失敗

🆔 交易ID: {trade_id}
❌ 錯誤: {e}
⚠️ 請立即手動檢查並平倉相關倉位！"""
            self.notifier.send_message(error_message)

    def check_close_conditions_for_trade(self, trade_info: Dict) -> bool:
        """檢查特定交易的平倉條件 - 使用固定阈值 0.05%出场"""
        current_prices = self.get_current_prices()
        if not current_prices or len(current_prices) < 2:
            return False

        current_spread = self.calculate_spread(
            current_prices['bybit'],
            current_prices['mt5']
        )

        # 價差小於等於 0.05% 時平倉
        return abs(current_spread['percentage']) <= self.close_spread_threshold
    
    def close_arbitrage_trade(self, trade_info: Dict) -> Optional[Dict]:
        """平倉套利交易"""
        trade_id = trade_info['trade_id']
        print(f"🔚 開始平倉交易 {trade_id}")
        
        current_prices = self.get_current_prices()
        if not current_prices or len(current_prices) < 2:
            print("❌ 無法獲取當前價格")
            return None
        
        close_result = {
            'trade_id': trade_id,
            'close_time': datetime.now(),
            'bybit_close_price': current_prices['bybit'],
            'mt5_close_price': current_prices['mt5']
        }
        
        try:
            # 平倉 Bybit 持倉
            print(f"📊 平倉 Bybit 持倉...")
            bybit_close_side = "Sell" if trade_info['bybit_side'] == "Buy" else "Buy"
            bybit_close_order = self.bybit_client.close_position(
                symbol="XAUTUSDT",
                side=bybit_close_side,
                qty=trade_info['bybit_qty']
            )
            
            if not bybit_close_order:
                print("❌ Bybit 平倉失敗")
                return None
            
            close_result['bybit_close_order'] = bybit_close_order
            
            # 平倉 MT5 持倉
            print(f"📊 平倉 MT5 持倉...")
            if not self.mt5_client.connect():
                print("❌ MT5 連接失敗")
                return None

            # 🚨 修正：獲取MT5持倉後不要立即斷開連接
            positions = self.mt5_client.get_positions("XAUUSD+")

            if not positions:
                print("❌ 找不到 MT5 持倉")
                self.mt5_client.disconnect()
                return None

            # 平倉第一個找到的 XAUUSD+ 持倉
            mt5_position = positions[0]
            print(f"🔍 找到MT5持倉: ticket {mt5_position['ticket']}, 數量 {mt5_position.get('volume')} 手")

            mt5_close_order = self.mt5_client.close_position(
                ticket=mt5_position['ticket']
            )

            # 平倉後斷開連接
            self.mt5_client.disconnect()

            if not mt5_close_order:
                print("❌ MT5 平倉失敗")
                return None

            print(f"✅ MT5 平倉成功: ticket {mt5_position['ticket']}")
            
            close_result['mt5_close_order'] = mt5_close_order
            
            # 計算收益
            duration = close_result['close_time'] - trade_info['start_time']
            
            # 計算 PnL (簡化計算)
            bybit_pnl = (close_result['bybit_close_price'] - trade_info['bybit_price']) * trade_info['bybit_qty']
            if trade_info['bybit_side'] == "Sell":
                bybit_pnl = -bybit_pnl
            
            mt5_pnl = (close_result['mt5_close_price'] - trade_info['mt5_price']) * trade_info['mt5_qty']
            if trade_info['mt5_side'] == "Sell":
                mt5_pnl = -mt5_pnl
            
            total_pnl = bybit_pnl + mt5_pnl
            roi = (total_pnl / trade_info['total_margin']) * 100
            
            close_result.update({
                'bybit_pnl': bybit_pnl,
                'mt5_pnl': mt5_pnl,
                'total_pnl': total_pnl,
                'roi': roi,
                'duration': str(duration)
            })
            
            # 從活躍交易中移除
            if trade_id in self.active_trades:
                del self.active_trades[trade_id]

            # 添加到交易歷史
            self.trade_history.append({**trade_info, **close_result})

            # 發送詳細的平倉通知
            self.send_closing_notification(trade_info, close_result)

            print(f"✅ 套利交易 {trade_id} 平倉成功")
            print(f"💰 總收益: {total_pnl:.2f} (ROI: {roi:.2f}%)")
            self.logger.info(f"套利交易 {trade_id} 平倉成功，收益: {total_pnl:.2f}")

            return close_result
            
        except Exception as e:
            print(f"❌ 平倉錯誤: {e}")
            self.notifier.send_error_notification(str(e), "套利交易平倉")
            return None
    
    def save_trade_data(self):
        """保存交易數據"""
        data = {
            'active_trades': self.active_trades,
            'trade_history': self.trade_history,
            'last_update': datetime.now().isoformat()
        }
        
        try:
            with open(self.trades_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            print(f"❌ 保存交易數據失敗: {e}")
    
    def detect_existing_positions(self):
        """檢測現有倉位狀況"""
        result = {
            'has_positions': False,
            'bybit_positions': 0,
            'mt5_positions': 0,
            'details': []
        }

        try:
            # 檢查Bybit倉位
            bybit_position = self.bybit_client.get_position_info('XAUTUSDT')
            if bybit_position and float(bybit_position.get('size', 0)) > 0:
                result['bybit_positions'] = 1
                result['has_positions'] = True
                result['details'].append(f"Bybit: {bybit_position.get('size')} 張 {bybit_position.get('side')}")

            # 檢查MT5倉位
            if self.mt5_client.connect():
                positions = self.mt5_client.get_positions("XAUUSD+")
                if positions:
                    result['mt5_positions'] = len(positions)
                    result['has_positions'] = True
                    for pos in positions:
                        pos_type = '買入' if pos.get('type', 0) == 0 else '賣出'
                        result['details'].append(f"MT5: {pos.get('volume')} 手 {pos_type}")

                self.mt5_client.disconnect()

        except Exception as e:
            print(f"⚠️ 檢測倉位時發生錯誤: {e}")

        return result

    def load_trade_data(self):
        """載入交易數據"""
        if not os.path.exists(self.trades_file):
            return

        try:
            with open(self.trades_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 注意：不再載入active_trades，因為我們已經重置了
                self.trade_history = data.get('trade_history', [])
                print(f"📚 載入交易歷史: {len(self.trade_history)} 筆記錄")
        except Exception as e:
            print(f"❌ 載入交易數據失敗: {e}")
    
    def run_arbitrage_system(self, interval: int = 60):
        """運行套利系統"""
        print("🚀 啟動 XAU 套利交易系統")
        print(f"📊 監控間隔: {interval} 秒")
        print(f"🎯 進場價差: {self.min_spread_threshold}%")
        print(f"🔚 平倉價差: {self.close_spread_threshold}%")
        print(f"⚠️ 熔断价差: {self.risk_spread_threshold}%")
        print(f"📈 槓桿倍數: {self.leverage}x")
        print(f"📦 最大倉位: {self.max_positions}")
        print("=" * 60)

        # 倉位檢測已在初始化時完成
        print("📊 系統啟動完成，開始監控套利機會...")

        try:
            while True:
                current_time = datetime.now()
                print(f"\n🔍 [{current_time.strftime('%Y-%m-%d %H:%M:%S')}] 檢查套利機會...")

                # 獲取當前價格
                prices = self.get_current_prices()
                if len(prices) < 2:
                    print("❌ 風控機制：無法獲取完整價格數據，跳過本次檢查")
                    print(f"   已獲取價格: {list(prices.keys())}")

                    # 在云端环境中，如果连续无法获取价格，发送警告但不退出
                    if hasattr(self, '_consecutive_price_failures'):
                        self._consecutive_price_failures += 1
                    else:
                        self._consecutive_price_failures = 1

                    if self._consecutive_price_failures >= 10:  # 连续10次失败
                        error_msg = f"连续{self._consecutive_price_failures}次无法获取完整价格数据"
                        print(f"⚠️ {error_msg}")
                        self.notifier.send_error_notification(error_msg, "价格获取")
                        self._consecutive_price_failures = 0  # 重置计数器

                    time.sleep(interval)
                    continue
                else:
                    # 成功获取价格，重置失败计数器
                    self._consecutive_price_failures = 0
                
                # 計算價差
                spread_info = self.calculate_spread(prices['bybit'], prices['mt5'])

                # 详细的价差监控日志
                print(f"💰 价格监控:")
                print(f"   Bybit XAUTUSDT: ${prices['bybit']:.2f}")
                print(f"   MT5 XAUUSD+:    ${prices['mt5']:.2f}")
                print(f"📊 价差分析:")
                print(f"   绝对价差: ${spread_info['absolute']:.2f}")
                print(f"   百分比价差: {spread_info['percentage']:.4f}%")
                print(f"   价差方向: {spread_info['direction']}")
                print(f"   进场阈值: {self.min_spread_threshold}%")
                print(f"   出场阈值: {self.close_spread_threshold}%")
                print(f"   熔断阈值: {self.risk_spread_threshold}%")

                # 判断是否接近进场条件
                abs_spread_pct = abs(spread_info['percentage'])

                if abs_spread_pct >= self.min_spread_threshold * 0.8:  # 80%阈值时提醒
                    print(f"⚠️ 价差接近进场阈值！当前: {abs_spread_pct:.4f}%, 需要: {self.min_spread_threshold}%")
                elif abs_spread_pct >= self.min_spread_threshold * 0.6:  # 60%阈值时提醒
                    print(f"📈 价差正在扩大: {abs_spread_pct:.4f}% (阈值的{abs_spread_pct/self.min_spread_threshold*100:.1f}%)")
                else:
                    print(f"📉 价差较小: {abs_spread_pct:.4f}% (阈值的{abs_spread_pct/self.min_spread_threshold*100:.1f}%)")

                # 獲取資金費率（僅供參考，不影響進場決策）
                funding_rate = self.bybit_client.get_funding_rate("XAUTUSDT")
                if funding_rate is not None:
                    print(f"💸 资金费率: {funding_rate:.6f} (仅供参考)")
                else:
                    print(f"⚠️ 无法获取资金费率")
                
                # 檢查風控條件
                if self.check_risk_control(spread_info):
                    print("🚨 觸發風控機制！價差過大，平倉所有倉位")
                    self.trigger_risk_control(spread_info)

                # 記錄到 CSV 和日誌
                trade_triggered = False

                # 檢查是否有活躍交易需要平倉
                trades_to_close = []
                for trade_id, trade_info in self.active_trades.items():
                    if self.check_close_conditions_for_trade(trade_info):
                        trades_to_close.append(trade_id)

                # 執行平倉
                for trade_id in trades_to_close:
                    if trade_id in self.active_trades:
                        close_result = self.close_arbitrage_trade(self.active_trades[trade_id])

                # 檢查新的套利機會（新策略：每分鐘檢測，符合條件就開倉）
                if self.check_arbitrage_conditions(spread_info, funding_rate or 0):
                    print("🚨 發現套利機會！")
                    trade_triggered = True

                    # 計算持倉大小
                    position_sizes = self.calculate_position_sizes(
                        prices['bybit'], prices['mt5']
                    )

                    if position_sizes:
                        # 執行套利交易
                        trade_result = self.execute_arbitrage_trade(
                            spread_info, prices, position_sizes
                        )

                        if trade_result:
                            print(f"✅ 新套利倉位已開啟，當前活躍倉位數: {len(self.active_trades)}")
                    else:
                        print("❌ 名義價值計算失敗，取消交易")
                        self.logger.error("名義價值計算失敗，取消交易")

                # 記錄數據
                self.log_spread_data(prices, spread_info, funding_rate or 0, trade_triggered)
                self.logger.info(f"價差監控 - Bybit: {prices['bybit']:.2f}, MT5: {prices['mt5']:.2f}, 價差: {spread_info['percentage']:.3f}%, 活躍倉位: {len(self.active_trades)}")

                # 顯示當前狀態
                print(f"📊 當前活躍套利倉位: {len(self.active_trades)}/{self.max_positions}")
                print(f"🏪 MT5市場狀態: {'開放' if self.mt5_market_open else '關閉'}")
                if self.is_risk_mode_active():
                    remaining_time = 24 * 3600 - (datetime.now() - self.risk_mode_start_time).total_seconds()
                    print(f"⚠️ 風控模式激活中，剩餘時間: {remaining_time/3600:.1f}小時")
                if not self.mt5_market_open:
                    print("⏸️ MT5市場關閉，暫停套利交易")
                
                # 保存交易數據
                self.save_trade_data()
                
                # 等待下次檢查
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n⏹️ 套利系統已停止")
        except Exception as e:
            error_msg = f"套利系統錯誤: {e}"
            print(f"❌ {error_msg}")

            # 发送错误通知
            try:
                self.notifier.send_error_notification(str(e), "套利系統運行")
            except:
                pass  # 避免通知失败导致程序崩溃

            # 在云端环境中，不要因为单次错误就退出
            # 而是记录错误并继续运行
            if hasattr(self, '_system_error_count'):
                self._system_error_count += 1
            else:
                self._system_error_count = 1

            print(f"⚠️ 系统错误计数: {self._system_error_count}")

            # 如果错误次数过多，才考虑退出
            if self._system_error_count >= 5:
                print("❌ 系统连续错误次数过多，停止运行")
                raise e
            else:
                print("🔄 系统将在30秒后重试...")
                time.sleep(30)
                # 重置一些状态，然后重新开始主循环
                self._consecutive_price_failures = 0
                return self.run_arbitrage_system(interval)

        finally:
            # 保存最終數據
            try:
                self.save_trade_data()
            except Exception as e:
                print(f"⚠️ 保存交易数据失败: {e}")

    def trigger_risk_control(self, spread_info: Dict):
        """觸發風控機制"""
        self.risk_mode = True
        self.risk_mode_start_time = datetime.now()

        # 平倉所有活躍倉位
        for trade_id in list(self.active_trades.keys()):
            if trade_id in self.active_trades:
                self.close_arbitrage_trade(self.active_trades[trade_id])

        # 發送風控通知
        message = f"🚨 風控機制觸發！\n"
        message += f"📊 異常價差: {spread_info['percentage']:.3f}%\n"
        message += f"⏰ 風控時間: 24小時\n"
        message += f"🔒 所有倉位已平倉，暫停套利交易"

        self.notifier.send_message(message)
        self.logger.warning(f"風控機制觸發，價差: {spread_info['percentage']:.3f}%")

    def send_opening_notification(self, trade_result: Dict):
        """發送開倉通知"""
        message = f"🚀 套利開倉通知\n"
        message += f"🆔 交易ID: {trade_result['trade_id']}\n"
        message += f"⏰ 開倉時間: {trade_result['start_time'].strftime('%Y-%m-%d %H:%M:%S')}\n"
        message += f"📊 價差: {trade_result['spread']:.3f}%\n"
        message += f"💰 Bybit價格: {trade_result['bybit_price']:.2f} ({trade_result['bybit_side']})\n"
        message += f"💰 MT5價格: {trade_result['mt5_price']:.2f} ({trade_result['mt5_side']})\n"
        message += f"📈 Bybit數量: {trade_result['bybit_qty']:.3f}\n"
        message += f"📈 MT5數量: {trade_result['mt5_qty']:.2f}\n"
        message += f"💵 單邊保證金: {trade_result['bybit_margin']:.2f} USD\n"
        message += f"💵 總保證金: {trade_result['total_margin']:.2f} USD\n"
        message += f"📈 槓桿倍數: {self.leverage}x\n"
        message += f"💸 預估手續費: {trade_result['bybit_fee'] + trade_result['mt5_fee']:.2f} USD\n"
        message += f"📊 當前活躍倉位: {len(self.active_trades)}/{self.max_positions}"

        self.notifier.send_message(message)

    def send_closing_notification(self, trade_info: Dict, close_result: Dict):
        """發送平倉通知"""
        duration_minutes = (close_result['close_time'] - trade_info['start_time']).total_seconds() / 60

        message = f"🏁 套利平倉通知\n"
        message += f"🆔 交易ID: {trade_info['trade_id']}\n"
        message += f"⏰ 平倉時間: {close_result['close_time'].strftime('%Y-%m-%d %H:%M:%S')}\n"
        message += f"⏱️ 持倉時間: {duration_minutes:.1f}分鐘\n"
        message += f"💰 開倉價差: {trade_info['spread']:.3f}%\n"
        message += f"💰 平倉Bybit: {close_result['bybit_close_price']:.2f}\n"
        message += f"💰 平倉MT5: {close_result['mt5_close_price']:.2f}\n"
        message += f"📈 Bybit盈虧: {close_result['bybit_pnl']:.2f} USD\n"
        message += f"📈 MT5盈虧: {close_result['mt5_pnl']:.2f} USD\n"
        message += f"💵 總盈虧: {close_result['total_pnl']:.2f} USD\n"
        message += f"📊 ROI: {close_result['roi']:.2f}%\n"
        message += f"📊 剩餘活躍倉位: {len(self.active_trades)}/{self.max_positions}"

        self.notifier.send_message(message)

    def start_trading(self, interval: int = 60):
        """启动交易系统 - run_arbitrage_system的别名"""
        return self.run_arbitrage_system(interval)

def main():
    trader = XAUArbitrageTrader()
    trader.run_arbitrage_system()

if __name__ == "__main__":
    main()