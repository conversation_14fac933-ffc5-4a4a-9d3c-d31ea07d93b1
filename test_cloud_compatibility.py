#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试云端兼容性
"""

import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cloud_config import get_mt5_client, is_cloud_environment, print_environment_info

def test_mt5_client_selection():
    """测试MT5客户端选择"""
    print("🧪 测试MT5客户端自动选择...")
    
    try:
        MT5ClientClass = get_mt5_client()
        client = MT5ClientClass()
        
        print(f"✅ 成功创建MT5客户端: {client.__class__.__name__}")
        
        # 测试基本功能
        if hasattr(client, 'connect'):
            connected = client.connect()
            print(f"连接测试: {'✅ 成功' if connected else '❌ 失败'}")
            
            if connected:
                # 测试获取价格
                price = client.get_current_price()
                print(f"价格获取: {'✅ 成功' if price else '❌ 失败'} (价格: {price})")
                
                # 测试账户信息
                account_info = client.get_account_info()
                print(f"账户信息: {'✅ 成功' if account_info else '❌ 失败'}")
                
                client.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ MT5客户端测试失败: {e}")
        return False

def test_cloud_simulation():
    """模拟云端环境测试"""
    print("🧪 模拟云端环境测试...")
    
    # 临时设置环境变量模拟云端
    original_env = os.environ.get('RAILWAY_ENVIRONMENT')
    os.environ['RAILWAY_ENVIRONMENT'] = 'production'
    
    try:
        # 重新导入以获取新的环境检测结果
        import importlib
        import cloud_config
        importlib.reload(cloud_config)
        
        is_cloud = cloud_config.is_cloud_environment()
        print(f"云端环境检测: {'✅ 正确' if is_cloud else '❌ 错误'}")
        
        # 测试客户端选择
        MT5ClientClass = cloud_config.get_mt5_client()
        client = MT5ClientClass()
        
        is_cloud_client = 'Cloud' in client.__class__.__name__
        print(f"云端客户端选择: {'✅ 正确' if is_cloud_client else '❌ 错误'}")
        
        return is_cloud and is_cloud_client
        
    except Exception as e:
        print(f"❌ 云端模拟测试失败: {e}")
        return False
    
    finally:
        # 恢复原始环境
        if original_env is None:
            os.environ.pop('RAILWAY_ENVIRONMENT', None)
        else:
            os.environ['RAILWAY_ENVIRONMENT'] = original_env

def test_requirements_selection():
    """测试requirements文件选择"""
    print("🧪 测试requirements文件选择...")
    
    try:
        from cloud_config import get_requirements_file
        
        # 本地环境测试
        req_file = get_requirements_file()
        expected_local = 'requirements.txt'
        local_correct = req_file == expected_local
        print(f"本地环境requirements: {'✅ 正确' if local_correct else '❌ 错误'} ({req_file})")
        
        # 模拟云端环境
        os.environ['HEROKU_APP_NAME'] = 'test-app'
        
        # 重新导入
        import importlib
        import cloud_config
        importlib.reload(cloud_config)
        
        req_file_cloud = cloud_config.get_requirements_file()
        expected_cloud = 'requirements-cloud.txt'
        cloud_correct = req_file_cloud == expected_cloud
        print(f"云端环境requirements: {'✅ 正确' if cloud_correct else '❌ 错误'} ({req_file_cloud})")
        
        # 清理环境变量
        os.environ.pop('HEROKU_APP_NAME', None)
        
        return local_correct and cloud_correct
        
    except Exception as e:
        print(f"❌ requirements选择测试失败: {e}")
        return False

def test_cloud_mt5_functionality():
    """测试云端MT5客户端功能"""
    print("🧪 测试云端MT5客户端功能...")
    
    try:
        from bybit_mt5_client_cloud import BybitMT5ClientCloud
        
        client = BybitMT5ClientCloud()
        
        # 测试连接
        connected = client.connect()
        print(f"连接功能: {'✅ 正常' if connected else '❌ 异常'}")
        
        if connected:
            # 测试价格获取
            price = client.get_current_price()
            price_ok = price is not None and price > 0
            print(f"价格获取: {'✅ 正常' if price_ok else '❌ 异常'} (价格: {price})")
            
            # 测试账户信息
            account_info = client.get_account_info()
            account_ok = account_info is not None and 'balance' in account_info
            print(f"账户信息: {'✅ 正常' if account_ok else '❌ 异常'}")
            
            # 测试下单
            order = client.place_order('XAUUSD', 'BUY', 0.01, price)
            order_ok = order is not None and 'order_id' in order
            print(f"模拟下单: {'✅ 正常' if order_ok else '❌ 异常'}")
            
            # 测试平仓
            if order_ok:
                close_result = client.close_position(order['order_id'])
                close_ok = close_result is not None
                print(f"模拟平仓: {'✅ 正常' if close_ok else '❌ 异常'}")
            
            # 测试市场状态
            market_open = client.is_market_open()
            print(f"市场状态: {'✅ 正常' if isinstance(market_open, bool) else '❌ 异常'}")
            
            client.disconnect()
            
            return connected and price_ok and account_ok and order_ok
        
        return False
        
    except Exception as e:
        print(f"❌ 云端MT5功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 云端兼容性测试")
    print("=" * 60)
    
    # 打印环境信息
    print_environment_info()
    
    results = []
    
    # 运行各项测试
    results.append(("MT5客户端选择", test_mt5_client_selection()))
    results.append(("云端环境模拟", test_cloud_simulation()))
    results.append(("Requirements选择", test_requirements_selection()))
    results.append(("云端MT5功能", test_cloud_mt5_functionality()))
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:<15}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 云端兼容性测试全部通过！")
        print("✅ 系统已准备好云端部署")
    else:
        print("⚠️ 部分测试失败，请检查配置")
    
    print("\n📋 部署建议:")
    print("1. 使用 requirements-cloud.txt 进行云端部署")
    print("2. 使用 start_cloud_strategy.py 启动云端版本")
    print("3. 配置必要的环境变量")
    print("4. 监控Telegram通知确保正常运行")

if __name__ == "__main__":
    main()
