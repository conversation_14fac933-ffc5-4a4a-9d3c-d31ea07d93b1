#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试无限重启修复
验证系统是否能正常启动而不会无限重启
"""

import os
import sys
import time
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cloud_config import print_environment_info, is_cloud_environment

def test_startup_notification():
    """测试启动通知去重机制"""
    print("=" * 50)
    print("🧪 测试启动通知去重机制")
    print("=" * 50)
    
    from start_cloud_strategy import should_send_startup_notification, record_startup_notification
    
    # 第一次应该发送通知
    should_send_1 = should_send_startup_notification()
    print(f"第一次检查是否应该发送通知: {should_send_1}")
    
    if should_send_1:
        record_startup_notification()
        print("✅ 记录了启动通知时间")
    
    # 立即再次检查，应该不发送
    should_send_2 = should_send_startup_notification()
    print(f"第二次检查是否应该发送通知: {should_send_2}")
    
    if not should_send_2:
        print("✅ 启动通知去重机制工作正常")
        return True
    else:
        print("❌ 启动通知去重机制可能有问题")
        return False

def test_cloud_mt5_client():
    """测试云端MT5客户端"""
    print("=" * 50)
    print("🧪 测试云端MT5客户端")
    print("=" * 50)
    
    try:
        from bybit_mt5_client_cloud import BybitMT5ClientCloud
        
        client = BybitMT5ClientCloud()
        
        # 测试连接
        connected = client.connect()
        print(f"MT5连接状态: {connected}")
        
        if connected:
            # 测试获取价格
            tick = client.get_tick("XAUUSD+")
            print(f"获取到的报价: {tick}")
            
            if tick and tick.get('bid', 0) > 0:
                print("✅ 云端MT5客户端工作正常")
                client.disconnect()
                return True
            else:
                print("❌ 无法获取有效报价")
        else:
            print("❌ 无法连接")
        
        client.disconnect()
        return False
        
    except Exception as e:
        print(f"❌ 云端MT5客户端测试失败: {e}")
        return False

def test_price_fetching():
    """测试价格获取功能"""
    print("=" * 50)
    print("🧪 测试价格获取功能")
    print("=" * 50)
    
    try:
        from xau_arbitrage_trader import XAUArbitrageTrader
        
        trader = XAUArbitrageTrader()
        
        # 测试获取价格
        prices = trader.get_current_prices()
        print(f"获取到的价格: {prices}")
        
        if len(prices) >= 2:
            print("✅ 价格获取成功")
            
            # 计算价差
            spread_info = trader.calculate_spread(prices['bybit'], prices['mt5'])
            print(f"价差信息: {spread_info}")
            
            return True
        else:
            print("❌ 价格获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 价格获取测试失败: {e}")
        return False

def test_system_stability():
    """测试系统稳定性（短时间运行）"""
    print("=" * 50)
    print("🧪 测试系统稳定性（运行30秒）")
    print("=" * 50)
    
    try:
        from xau_arbitrage_trader import XAUArbitrageTrader
        
        trader = XAUArbitrageTrader()
        
        print("开始短时间运行测试...")
        start_time = time.time()
        
        # 运行30秒
        while time.time() - start_time < 30:
            try:
                prices = trader.get_current_prices()
                if len(prices) >= 2:
                    spread_info = trader.calculate_spread(prices['bybit'], prices['mt5'])
                    print(f"价差: {spread_info['percentage']:.3f}%")
                else:
                    print("价格获取失败，但系统继续运行")
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                print(f"运行中出现错误: {e}")
                # 继续运行，不退出
                time.sleep(5)
        
        print("✅ 系统稳定性测试完成，未发生崩溃")
        return True
        
    except Exception as e:
        print(f"❌ 系统稳定性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始无限重启修复测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 打印环境信息
    print_environment_info()
    
    # 运行各项测试
    tests = [
        ("启动通知去重", test_startup_notification),
        ("云端MT5客户端", test_cloud_mt5_client),
        ("价格获取功能", test_price_fetching),
        ("系统稳定性", test_system_stability),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 打印测试结果摘要
    print("\n" + "=" * 50)
    print("📊 测试结果摘要")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统应该不会无限重启了")
        print("\n📋 修复内容:")
        print("1. ✅ 修改Railway重启策略为 'on_failure'")
        print("2. ✅ 添加启动通知去重机制")
        print("3. ✅ 改进云端环境错误处理")
        print("4. ✅ 使用云端兼容的MT5客户端")
        print("5. ✅ 增强系统稳定性")
    else:
        print("⚠️ 部分测试失败，可能仍有重启问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 测试完成，结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
