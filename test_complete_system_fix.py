#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统修复测试
验证：
1. 市场状态检测（基于东八区时间）
2. 价格获取（正确区分两个来源）
3. 主流程完整性（监控-交易-平倉-紀錄）
"""

import os
import sys
import time
from datetime import datetime
import pytz

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_market_status_detection():
    """测试市场状态检测"""
    print("=" * 60)
    print("🧪 测试市场状态检测（基于东八区时间）")
    print("=" * 60)
    
    try:
        from bybit_mt5_client_cloud import BybitMT5ClientCloud
        
        client = BybitMT5ClientCloud()
        
        if client.connect():
            print("✅ 云端MT5客户端连接成功")
            
            # 获取当前东八区时间
            tz_beijing = pytz.timezone('Asia/Shanghai')
            now_beijing = datetime.now(tz_beijing)
            weekday = now_beijing.weekday()
            weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            
            print(f"📅 当前东八区时间: {now_beijing.strftime('%Y-%m-%d %H:%M:%S')} ({weekday_names[weekday]})")
            
            # 测试symbol_info
            symbol_info = client.get_symbol_info("XAUUSD+")
            if symbol_info:
                trade_mode = symbol_info.get('trade_mode', 0)
                is_weekend = weekday in [5, 6]
                expected_mode = 0 if is_weekend else 4
                
                print(f"📊 交易模式: {trade_mode}")
                print(f"🕒 是否周末: {is_weekend}")
                print(f"🎯 预期模式: {expected_mode}")
                
                if trade_mode == expected_mode:
                    print("✅ 市场状态检测正确")
                    client.disconnect()
                    return True
                else:
                    print(f"❌ 市场状态检测错误: 实际{trade_mode} vs 预期{expected_mode}")
                    client.disconnect()
                    return False
            else:
                print("❌ 无法获取symbol_info")
                client.disconnect()
                return False
        else:
            print("❌ 云端MT5客户端连接失败")
            return False
    except Exception as e:
        print(f"❌ 市场状态检测测试异常: {e}")
        return False

def test_price_sources_separation():
    """测试价格来源正确分离"""
    print("=" * 60)
    print("🧪 测试价格来源正确分离")
    print("=" * 60)
    
    try:
        import requests
        
        # 测试Bybit永续合约XAUTUSDT价格
        print("🔍 测试Bybit永续合约XAUTUSDT价格获取...")
        response = requests.get(
            'https://api.bybit.com/v5/market/tickers',
            params={'category': 'linear', 'symbol': 'XAUTUSDT'},
            timeout=10
        )
        
        bybit_futures_price = None
        if response.status_code == 200:
            data = response.json()
            if data.get('retCode') == 0 and data.get('result', {}).get('list'):
                ticker = data['result']['list'][0]
                bybit_futures_price = float(ticker['lastPrice'])
                print(f"✅ Bybit永续合约XAUTUSDT: ${bybit_futures_price:.2f}")
        
        if not bybit_futures_price:
            print("❌ 无法获取Bybit永续合约价格")
            return False
        
        # 测试Bybit现货XAUUSDT价格
        print("🔍 测试Bybit现货XAUUSDT价格获取...")
        response = requests.get(
            'https://api.bybit.com/v5/market/tickers',
            params={'category': 'spot', 'symbol': 'XAUUSDT'},
            timeout=10
        )
        
        bybit_spot_price = None
        if response.status_code == 200:
            data = response.json()
            if data.get('retCode') == 0 and data.get('result', {}).get('list'):
                ticker = data['result']['list'][0]
                bybit_spot_price = float(ticker['lastPrice'])
                print(f"✅ Bybit现货XAUUSDT: ${bybit_spot_price:.2f}")
        
        # 测试云端MT5客户端价格获取
        print("🔍 测试云端MT5客户端价格获取...")
        from bybit_mt5_client_cloud import BybitMT5ClientCloud
        
        client = BybitMT5ClientCloud()
        if client.connect():
            mt5_price = client.get_current_price("XAUUSD+")
            client.disconnect()
            
            if mt5_price:
                print(f"✅ MT5等效价格: ${mt5_price:.2f}")
                
                # 检查价格差异
                if bybit_spot_price:
                    spot_diff = abs(mt5_price - bybit_spot_price)
                    print(f"📊 与现货价差: ${spot_diff:.2f}")
                
                futures_diff = abs(mt5_price - bybit_futures_price)
                print(f"📊 与永续合约价差: ${futures_diff:.2f}")
                
                # MT5价格应该与现货价格更接近，或者是永续合约价格的调整版本
                if bybit_spot_price and spot_diff < futures_diff:
                    print("✅ MT5价格使用现货价格，符合预期")
                elif futures_diff < bybit_futures_price * 0.01:  # 1%以内的调整
                    print("✅ MT5价格基于永续合约调整，符合预期")
                else:
                    print("⚠️ MT5价格来源需要确认")
                
                return True
            else:
                print("❌ 无法获取MT5等效价格")
                return False
        else:
            print("❌ 云端MT5客户端连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 价格来源分离测试异常: {e}")
        return False

def test_arbitrage_trader_integration():
    """测试套利交易者集成"""
    print("=" * 60)
    print("🧪 测试套利交易者集成")
    print("=" * 60)
    
    try:
        from xau_arbitrage_trader import XAUArbitrageTrader
        
        trader = XAUArbitrageTrader()
        
        # 测试价格获取
        print("🔍 测试价格获取...")
        prices = trader.get_current_prices()
        
        if 'bybit' in prices and 'mt5' in prices:
            bybit_price = prices['bybit']
            mt5_price = prices['mt5']
            
            print(f"✅ Bybit永续合约价格: ${bybit_price:.2f}")
            print(f"✅ MT5价格: ${mt5_price:.2f}")
            
            # 测试价差计算
            spread_info = trader.calculate_spread(bybit_price, mt5_price)
            print(f"📊 价差计算:")
            print(f"   绝对价差: ${spread_info['absolute']:.2f}")
            print(f"   百分比价差: {spread_info['percentage']:.4f}%")
            print(f"   价差方向: {spread_info['direction']}")
            
            # 测试市场状态检查
            print("🔍 测试市场状态检查...")
            market_open = trader.check_mt5_market_status()
            print(f"📊 MT5市场状态: {'开放' if market_open else '关闭'}")
            
            # 测试套利条件检查
            print("🔍 测试套利条件检查...")
            funding_rate = trader.bybit_client.get_funding_rate("XAUTUSDT") or 0
            arbitrage_conditions = trader.check_arbitrage_conditions(spread_info, funding_rate)
            print(f"📊 套利条件: {'满足' if arbitrage_conditions else '不满足'}")
            
            # 检查价差是否合理
            abs_percentage = abs(spread_info['percentage'])
            if abs_percentage < 5.0:  # 价差应该小于5%
                print(f"✅ 价差在合理范围内 ({abs_percentage:.4f}% < 5%)")
                return True
            else:
                print(f"❌ 价差过大 ({abs_percentage:.4f}% >= 5%)")
                return False
        else:
            print("❌ 无法获取完整的价格数据")
            print(f"   获取到的价格: {list(prices.keys())}")
            return False
            
    except Exception as e:
        print(f"❌ 套利交易者集成测试异常: {e}")
        return False

def test_main_loop_simulation():
    """测试主循环模拟（短时间运行）"""
    print("=" * 60)
    print("🧪 测试主循环模拟（10秒运行）")
    print("=" * 60)
    
    try:
        from xau_arbitrage_trader import XAUArbitrageTrader
        import threading
        
        trader = XAUArbitrageTrader()
        
        # 创建标志来监控主循环
        loop_count = 0
        errors = []
        
        # 重写一些方法来监控执行
        original_get_prices = trader.get_current_prices
        
        def monitored_get_prices():
            nonlocal loop_count
            loop_count += 1
            print(f"🔄 主循环第 {loop_count} 次迭代")
            try:
                return original_get_prices()
            except Exception as e:
                errors.append(f"价格获取错误: {e}")
                return {}
        
        trader.get_current_prices = monitored_get_prices
        
        # 在单独线程中运行交易系统
        def run_trader():
            try:
                trader.run_arbitrage_system(interval=5)  # 5秒间隔
            except Exception as e:
                errors.append(f"主循环错误: {e}")
        
        trader_thread = threading.Thread(target=run_trader, daemon=True)
        trader_thread.start()
        
        # 运行10秒
        print("⏱️ 运行10秒监控主循环...")
        time.sleep(10)
        
        print(f"📊 测试结果:")
        print(f"   循环次数: {loop_count}")
        print(f"   错误数量: {len(errors)}")
        
        if errors:
            print("❌ 发现错误:")
            for error in errors:
                print(f"   - {error}")
        
        if loop_count >= 1 and len(errors) == 0:
            print("✅ 主循环运行正常")
            return True
        elif loop_count >= 1:
            print("⚠️ 主循环运行但有错误")
            return False
        else:
            print("❌ 主循环未运行")
            return False
            
    except Exception as e:
        print(f"❌ 主循环模拟测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始完整系统修复测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行各项测试
    tests = [
        ("市场状态检测", test_market_status_detection),
        ("价格来源分离", test_price_sources_separation),
        ("套利交易者集成", test_arbitrage_trader_integration),
        ("主循环模拟", test_main_loop_simulation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 打印测试结果摘要
    print("\n" + "=" * 60)
    print("📊 完整系统修复测试结果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统已完全修复")
        print("\n🔧 修复内容:")
        print("1. ✅ 修复云端市场状态检测（基于东八区时间）")
        print("2. ✅ 正确分离价格来源（永续合约 vs MT5）")
        print("3. ✅ 确保主流程完整性")
        print("4. ✅ 添加详细的监控和错误处理")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 测试完成，结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
