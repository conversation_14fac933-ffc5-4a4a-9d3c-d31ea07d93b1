#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主循环修复
验证run_arbitrage_system方法是否能正确执行主循环
"""

import os
import sys
import time
import threading
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from xau_arbitrage_trader import XAUArbitrageTrader

def test_main_loop_execution():
    """测试主循环是否能正确执行"""
    print("=" * 60)
    print("🧪 测试主循环执行")
    print("=" * 60)
    
    trader = XAUArbitrageTrader()
    
    # 创建一个标志来检测主循环是否开始
    loop_started = False
    loop_iterations = 0
    
    # 重写一些方法来监控执行
    original_get_prices = trader.get_current_prices
    original_calculate_spread = trader.calculate_spread
    
    def mock_get_prices():
        nonlocal loop_started, loop_iterations
        loop_started = True
        loop_iterations += 1
        print(f"🔍 主循环第 {loop_iterations} 次迭代")
        
        # 返回模拟价格
        return {
            'bybit': 2650.0,
            'mt5': 2651.0
        }
    
    def mock_calculate_spread(bybit_price, mt5_price):
        spread = mt5_price - bybit_price
        percentage = (spread / bybit_price) * 100
        return {
            'absolute': spread,
            'percentage': percentage,
            'direction': 'MT5 > Bybit' if spread > 0 else 'Bybit > MT5'
        }
    
    # 替换方法
    trader.get_current_prices = mock_get_prices
    trader.calculate_spread = mock_calculate_spread
    
    # 在单独的线程中运行交易系统
    def run_trader():
        try:
            trader.run_arbitrage_system(interval=5)  # 5秒间隔用于测试
        except Exception as e:
            print(f"交易系统异常: {e}")
    
    trader_thread = threading.Thread(target=run_trader, daemon=True)
    trader_thread.start()
    
    # 等待并检查主循环是否开始
    print("等待主循环开始...")
    start_time = time.time()
    
    while not loop_started and time.time() - start_time < 30:
        time.sleep(1)
    
    if loop_started:
        print("✅ 主循环已开始执行")
        
        # 等待几次迭代
        print("等待几次迭代...")
        time.sleep(15)  # 等待3次迭代 (5秒间隔)
        
        if loop_iterations >= 2:
            print(f"✅ 主循环正常运行，已完成 {loop_iterations} 次迭代")
            return True
        else:
            print(f"❌ 主循环迭代次数不足: {loop_iterations}")
            return False
    else:
        print("❌ 主循环未开始执行")
        return False

def test_price_monitoring_logs():
    """测试价格监控日志输出"""
    print("=" * 60)
    print("🧪 测试价格监控日志输出")
    print("=" * 60)
    
    trader = XAUArbitrageTrader()
    
    # 测试价格获取
    prices = trader.get_current_prices()
    print(f"获取到的价格: {prices}")
    
    if len(prices) >= 2:
        # 测试价差计算和日志输出
        spread_info = trader.calculate_spread(prices['bybit'], prices['mt5'])
        
        print("\n模拟价差监控日志输出:")
        print(f"💰 价格监控:")
        print(f"   Bybit XAUTUSDT: ${prices['bybit']:.2f}")
        print(f"   MT5 XAUUSD+:    ${prices['mt5']:.2f}")
        print(f"📊 价差分析:")
        print(f"   绝对价差: ${spread_info['absolute']:.2f}")
        print(f"   百分比价差: {spread_info['percentage']:.4f}%")
        print(f"   价差方向: {spread_info['direction']}")
        print(f"   进场阈值: {trader.min_spread_threshold}%")
        
        # 判断是否接近进场条件
        abs_spread_pct = abs(spread_info['percentage'])
        if abs_spread_pct >= trader.min_spread_threshold * 0.8:
            print(f"⚠️ 价差接近进场阈值！当前: {abs_spread_pct:.4f}%, 需要: {trader.min_spread_threshold}%")
        elif abs_spread_pct >= trader.min_spread_threshold * 0.6:
            print(f"📈 价差正在扩大: {abs_spread_pct:.4f}% (阈值的{abs_spread_pct/trader.min_spread_threshold*100:.1f}%)")
        else:
            print(f"📉 价差较小: {abs_spread_pct:.4f}% (阈值的{abs_spread_pct/trader.min_spread_threshold*100:.1f}%)")
        
        print("✅ 价格监控日志输出正常")
        return True
    else:
        print("❌ 无法获取价格数据")
        return False

def test_method_compatibility():
    """测试方法兼容性"""
    print("=" * 60)
    print("🧪 测试方法兼容性")
    print("=" * 60)
    
    trader = XAUArbitrageTrader()
    
    # 检查方法是否存在
    methods_to_check = [
        'run_arbitrage_system',
        'start_trading',
        'get_current_prices',
        'calculate_spread'
    ]
    
    all_methods_exist = True
    
    for method_name in methods_to_check:
        if hasattr(trader, method_name):
            print(f"✅ {method_name} 方法存在")
        else:
            print(f"❌ {method_name} 方法不存在")
            all_methods_exist = False
    
    # 测试start_trading是否是run_arbitrage_system的别名
    if hasattr(trader, 'start_trading') and hasattr(trader, 'run_arbitrage_system'):
        print("✅ start_trading 和 run_arbitrage_system 方法都存在")
    
    return all_methods_exist

def main():
    """主测试函数"""
    print("🚀 开始主循环修复测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行各项测试
    tests = [
        ("方法兼容性", test_method_compatibility),
        ("价格监控日志", test_price_monitoring_logs),
        ("主循环执行", test_main_loop_execution),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 打印测试结果摘要
    print("\n" + "=" * 60)
    print("📊 主循环修复测试结果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！主循环问题已修复")
        print("\n🔧 修复内容:")
        print("1. ✅ 修复run_arbitrage_system方法的主循环")
        print("2. ✅ 添加详细的价差监控日志")
        print("3. ✅ 保持方法兼容性")
        print("4. ✅ 增强错误处理和监控")
    else:
        print("⚠️ 部分测试失败，可能仍有问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 测试完成，结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
