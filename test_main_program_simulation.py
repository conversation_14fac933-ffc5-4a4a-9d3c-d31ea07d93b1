#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模擬主程式下單流程測試
"""

import os
import sys
from dotenv import load_dotenv
from cloud_config import get_mt5_client
from bybit_futures_client import BybitFuturesClient

load_dotenv('config.env')

def simulate_main_program_order():
    """模擬主程式的下單流程"""
    print("🧪 模擬主程式下單流程...")
    
    # 初始化客戶端（與主程式相同）
    bybit_client = BybitFuturesClient()
    MT5ClientClass = get_mt5_client()
    mt5_client = MT5ClientClass()
    
    # 模擬主程式的下單參數
    trade_id = "test123"
    mt5_side = "Sell"  # 模擬主程式的賣出
    mt5_qty = 0.01     # 最小下單量
    
    print(f"📊 模擬下單參數:")
    print(f"   交易ID: {trade_id}")
    print(f"   方向: {mt5_side}")
    print(f"   數量: {mt5_qty}")
    
    # 模擬主程式的 MT5 下單流程
    print(f"📊 執行 MT5 {mt5_side} 訂單...")
    
    # 使用與主程式相同的連接方式
    if not mt5_client.connect():
        print("❌ MT5 連接失敗")
        return False
    
    mt5_order_type = 0 if mt5_side == "Buy" else 1  # 0=Buy, 1=Sell
    
    # 強制使用最小下單量，避免保證金不足
    mt5_qty = max(0.01, mt5_qty)  # 至少0.01手
    print(f"📊 MT5 下單量調整為: {mt5_qty} 手")
    
    # 使用與主程式完全相同的下單參數
    mt5_order = mt5_client.place_order(
        symbol="XAUUSD+",
        order_type=mt5_order_type,
        volume=mt5_qty,
        comment=f"Arbitrage {trade_id}"
        # 不指定 type_filling，讓 MT5 客戶端自動選擇
    )
    
    # 立即斷開連接
    mt5_client.disconnect()
    
    if not mt5_order:
        print("❌ MT5 訂單執行失敗")
        print("🔍 可能原因:")
        print("   1. 保證金不足")
        print("   2. 下單量過大")
        print("   3. 券商限制")
        print("   4. 市場狀態異常")
        print("   5. 連接狀態問題")
        return False
    
    print("✅ MT5 下單成功！")
    print(f"   訂單號: {mt5_order.get('order', 'Unknown')}")
    
    # 立即平倉測試訂單
    print("🔄 立即平倉測試訂單...")
    if mt5_client.connect():
        close_result = mt5_client.close_position(ticket=mt5_order.get('order', 0))
        mt5_client.disconnect()
        
        if close_result:
            print("✅ 測試訂單平倉成功")
        else:
            print("❌ 測試訂單平倉失敗")
    else:
        print("❌ 無法連接 MT5 平倉")
    
    return True

if __name__ == "__main__":
    simulate_main_program_order() 