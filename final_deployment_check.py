#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终部署前检查
"""

import os
import sys
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_required_files():
    """检查必需文件是否存在"""
    print("🔍 检查必需文件...")
    
    required_files = [
        'xau_arbitrage_trader.py',
        'bybit_futures_client.py', 
        'bybit_mt5_client.py',
        'telegram_notifier.py',
        'start_new_strategy.py',
        'config.env.example',
        'requirements.txt',
        'README.md',
        'DEPLOYMENT_CHECKLIST.md',
        'STRATEGY_UPGRADE_SUMMARY.md'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 缺失")
            missing_files.append(file)
    
    return len(missing_files) == 0

def check_configuration():
    """检查配置文件"""
    print("\n🔍 检查配置...")
    
    if os.path.exists('config.env'):
        print("✅ config.env 存在")
        
        # 检查关键配置项
        with open('config.env', 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_configs = [
            'BYBIT_API_KEY',
            'BYBIT_API_SECRET', 
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_CHAT_ID'
        ]
        
        missing_configs = []
        for config in required_configs:
            if config in content and not content.split(f'{config}=')[1].split('\n')[0].strip() == '':
                print(f"✅ {config} 已配置")
            else:
                print(f"❌ {config} 未配置或为空")
                missing_configs.append(config)
        
        return len(missing_configs) == 0
    else:
        print("❌ config.env 不存在")
        print("ℹ️ 请复制 config.env.example 并填入真实配置")
        return False

def check_system_functionality():
    """检查系统功能"""
    print("\n🔍 检查系统功能...")
    
    try:
        # 导入主要模块
        from xau_arbitrage_trader import XAUArbitrageTrader
        from bybit_futures_client import BybitFuturesClient
        from bybit_mt5_client import BybitMT5Client
        from telegram_notifier import TelegramNotifier
        
        print("✅ 所有模块导入成功")
        
        # 检查策略参数
        trader = XAUArbitrageTrader()
        
        checks = [
            ("进场阈值", trader.min_spread_threshold == 0.3),
            ("出场阈值", trader.close_spread_threshold == 0.1),
            ("风控阈值", trader.risk_spread_threshold == 0.7),
            ("杠杆倍数", trader.leverage == 20),
            ("最大仓位", trader.max_positions == 5),
            ("资金比例", trader.position_size_ratio == 0.02)
        ]
        
        all_correct = True
        for name, condition in checks:
            if condition:
                print(f"✅ {name} 配置正确")
            else:
                print(f"❌ {name} 配置错误")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def check_weekend_detection():
    """检查周末检测功能"""
    print("\n🔍 检查周末检测功能...")
    
    try:
        from xau_arbitrage_trader import XAUArbitrageTrader
        trader = XAUArbitrageTrader()
        
        # 检查周末检测方法是否存在
        if hasattr(trader, 'check_weekend_status'):
            print("✅ 周末检测功能存在")
            
            # 测试功能
            result = trader.check_weekend_status()
            now = datetime.now()
            weekday = now.weekday()
            is_weekend = weekday in [5, 6]
            
            if result == (not is_weekend):
                print(f"✅ 周末检测功能正常 (当前: {'周末' if is_weekend else '工作日'})")
                return True
            else:
                print(f"❌ 周末检测功能异常")
                return False
        else:
            print("❌ 周末检测功能不存在")
            return False
            
    except Exception as e:
        print(f"❌ 周末检测功能测试失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🚀 最终部署前检查")
    print("=" * 60)
    
    checks = [
        ("必需文件", check_required_files()),
        ("配置文件", check_configuration()),
        ("系统功能", check_system_functionality()),
        ("周末检测", check_weekend_detection())
    ]
    
    print("\n" + "=" * 60)
    print("📊 检查结果总结")
    print("=" * 60)
    
    passed = 0
    for name, result in checks:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:<10}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(checks)} 项检查通过")
    
    if passed == len(checks):
        print("\n🎉 系统检查完成！准备部署到云端服务器")
        print("\n📋 部署步骤:")
        print("1. 将代码上传到云端服务器")
        print("2. 安装依赖: pip install -r requirements.txt")
        print("3. 配置 config.env 文件")
        print("4. 运行: python start_new_strategy.py")
        print("\n⚠️ 重要提醒:")
        print("- 确保云端服务器已安装 MetaTrader5")
        print("- 确保网络连接稳定")
        print("- 建议先用小资金测试")
        print("- 密切监控 Telegram 通知")
    else:
        print("\n⚠️ 系统检查未完全通过，请修复问题后再部署")

if __name__ == "__main__":
    main()
