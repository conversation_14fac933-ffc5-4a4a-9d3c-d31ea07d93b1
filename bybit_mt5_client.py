import MetaTrader5 as mt5
import time
import os
from datetime import datetime
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv

load_dotenv('config.env')

class BybitMT5Client:
    def __init__(self):
        self.login = os.getenv('BYBIT_MT5_LOGIN')
        self.password = os.getenv('BYBIT_MT5_PASSWORD')
        self.server = os.getenv('BYBIT_MT5_SERVER', 'Bybit-Live')
        self.path = os.getenv('BYBIT_MT5_PATH', 'C:\\Program Files\\MetaTrader 5\\terminal64.exe')
        self.connected = False
        
    def connect(self) -> bool:
        """連接到 Bybit MT5 平台"""
        try:
            # 初始化 MT5
            if not mt5.initialize(path=self.path):
                print(f"❌ MT5 初始化失敗: {mt5.last_error()}")
                return False
            
            # 登入
            if not mt5.login(login=int(self.login), password=self.password, server=self.server):
                print(f"❌ MT5 登入失敗: {mt5.last_error()}")
                return False
            
            print(f"✅ Bybit MT5 連接成功")
            print(f"   伺服器: {self.server}")
            print(f"   帳號: {self.login}")
            
            self.connected = True
            return True
            
        except Exception as e:
            print(f"❌ MT5 連接錯誤: {e}")
            return False
    
    def disconnect(self):
        """斷開 MT5 連接"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            print("🔌 MT5 連接已斷開")
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """獲取交易對資訊"""
        if not self.connected:
            print("❌ MT5 未連接")
            return None
        
        try:
            # 選擇交易對
            if not mt5.symbol_select(symbol, True):
                print(f"❌ 無法選擇交易對 {symbol}: {mt5.last_error()}")
                return None
            
            # 獲取交易對資訊
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                print(f"❌ 無法獲取 {symbol} 資訊")
                return None
            
            return {
                'name': symbol_info.name,
                'bid': symbol_info.bid,
                'ask': symbol_info.ask,
                'last': symbol_info.last,
                'volume': symbol_info.volume,
                'spread': symbol_info.ask - symbol_info.bid,
                'trade_mode': symbol_info.trade_mode,
                'visible': symbol_info.visible,
                'select': symbol_info.select
            }
            
        except Exception as e:
            print(f"❌ 獲取 {symbol} 資訊錯誤: {e}")
            return None
    
    def get_tick(self, symbol: str) -> Optional[Dict[str, Any]]:
        """獲取即時報價"""
        if not self.connected:
            print("❌ MT5 未連接")
            return None
        
        try:
            # 選擇交易對
            if not mt5.symbol_select(symbol, True):
                print(f"❌ 無法選擇交易對 {symbol}")
                return None
            
            # 獲取即時報價
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                print(f"❌ 無法獲取 {symbol} 報價")
                return None
            
            return {
                'symbol': symbol,
                'bid': tick.bid,
                'ask': tick.ask,
                'last': tick.last,
                'volume': tick.volume,
                'time': datetime.fromtimestamp(tick.time),
                'spread': tick.ask - tick.bid
            }
            
        except Exception as e:
            print(f"❌ 獲取 {symbol} 報價錯誤: {e}")
            return None
    
    def get_current_price(self, symbol: str = "XAUUSD") -> Optional[float]:
        """获取当前价格（兼容云端版本的方法名）"""
        if symbol in ["XAUUSD", "XAUUSD+"]:
            tick = self.get_tick("XAUUSD+")
            if tick:
                return (tick['bid'] + tick['ask']) / 2
        return None

    def get_usdjpy_price(self) -> Optional[Dict[str, Any]]:
        """獲取 USDJPY 價格"""
        # 嘗試不同的 USDJPY 代號
        symbols_to_try = ['USDJPY+', 'USDJPY', 'USDJPY.m']
        
        for symbol in symbols_to_try:
            tick = self.get_tick(symbol)
            if tick:
                return {
                    'bid': tick['bid'],
                    'ask': tick['ask'],
                    'last': tick['last'],
                    'spread': tick['spread'],
                    'symbol': symbol
                }
        
        print("❌ 無法獲取 USDJPY 價格，請檢查交易對代號")
        return None
    
    def get_available_symbols(self) -> list:
        """獲取所有可用的交易對"""
        if not self.connected:
            print("❌ MT5 未連接")
            return []
        
        try:
            symbols = mt5.symbols_get()
            if symbols is None:
                print("❌ 無法獲取交易對列表")
                return []
            
            # 過濾出外匯對
            forex_symbols = []
            for symbol in symbols:
                if hasattr(symbol, 'name') and symbol.name:
                    forex_symbols.append(symbol.name)
            
            return forex_symbols[:50]  # 只顯示前50個
            
        except Exception as e:
            print(f"❌ 獲取交易對列表錯誤: {e}")
            return []
    
    def place_order(self, symbol: str, order_type: int, volume: float, price: float = None, comment: str = "", type_filling: int = None):
        """下單（強制FOK）"""
        import MetaTrader5 as mt5
        symbol_info = mt5.symbol_info(symbol)
        allowed_mode = symbol_info.filling_mode if symbol_info else None
        print(f"[DEBUG] {symbol} 允許的filling_mode: {allowed_mode}")
        # 只用允許的第一個模式（通常是1或0）
        if allowed_mode is not None:
            if allowed_mode & 1:
                filling_mode = 1  # FOK
            elif allowed_mode & 2:
                filling_mode = 2  # IOC
            elif allowed_mode & 4:
                filling_mode = 0  # RETURN
            else:
                filling_mode = 1  # 預設FOK
        else:
            filling_mode = 1
        if type_filling is not None:
            filling_mode = type_filling
        filling_mode = 1 # 強制FOK
        try:
            # 確保 MT5 連接
            if not self.connect():
                print("❌ MT5 連接失敗")
                return None
            # 獲取當前價格
            current_tick = self.get_tick(symbol)
            if not current_tick:
                print(f"❌ 無法獲取 {symbol} 價格")
                return None
            # 如果沒有指定價格，使用市價
            if price is None:
                if order_type == 0:  # Buy
                    price = current_tick['ask']
                else:  # Sell
                    price = current_tick['bid']
            print(f"🔍 開始 MT5 下單流程 - 交易對: {symbol}")
            print(f"✅ 交易對 {symbol} 選擇成功")
            print(f"   市價獲取成功: {price}")
            # 準備下單請求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,  # 0=Buy, 1=Sell
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": filling_mode,
            }
            print(f"📊 MT5 下單 - 交易對: {symbol}, 類型: {'買入' if order_type == 0 else '賣出'}, 數量: {volume}, 價格: {price}")
            print(f"📋 下單請求: {request}")
            # 執行下單
            result = mt5.order_send(request)
            if result is None:
                print("❌ MT5 下單失敗: order_send 返回 None")
                print(f"   最後錯誤: {mt5.last_error()}")
                return None
            print(f"📊 下單回應: retcode={result.retcode}, comment={result.comment}")
            # 檢查下單結果
            if result.retcode in [mt5.TRADE_RETCODE_DONE, mt5.TRADE_RETCODE_DONE_PARTIAL, 
                                mt5.TRADE_RETCODE_PLACED]:
                print(f"✅ MT5 下單成功！")
                print(f"   訂單號: {result.order}")
                print(f"   成交數量: {result.volume}")
                print(f"   成交價格: {result.price}")
                return {
                    "order": result.order,
                    "volume": result.volume,
                    "price": result.price,
                    "retcode": result.retcode
                }
            else:
                print(f"❌ MT5 下單失敗: {result.comment} (retcode: {result.retcode})")
                if result.retcode == 10027:
                    print("💡 提示: 請確保 MT5 客戶端的「自動交易」按鈕已開啟（變綠色）")
                return None
        except Exception as e:
            print(f"❌ MT5 下單異常: {e}")
            return None
    
    def close_position(self, ticket: int, volume: float = 0) -> Optional[Dict]:
        """平倉"""
        if not self.connected:
            print("❌ MT5 未連接")
            return None
        
        try:
            # 獲取持倉資訊
            position = mt5.positions_get(ticket=ticket)
            if not position:
                print(f"❌ 找不到持倉 {ticket}")
                return None
            
            position = position[0]
            close_volume = volume if volume > 0 else position.volume
            
            # 獲取當前市價用於平倉
            current_tick = self.get_tick(position.symbol)
            if not current_tick:
                print(f"❌ 無法獲取 {position.symbol} 價格用於平倉")
                return None
            
            # 平倉價格：買入持倉用bid價賣出，賣出持倉用ask價買入
            if position.type == 0:  # 買入持倉，用bid價賣出
                close_price = current_tick['bid']
            else:  # 賣出持倉，用ask價買入
                close_price = current_tick['ask']
            
            # 準備平倉請求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": close_volume,
                "type": mt5.ORDER_TYPE_BUY if position.type == 1 else mt5.ORDER_TYPE_SELL,
                "position": ticket,
                "price": close_price,  # 使用正確的市價
                "deviation": 20,
                "magic": 234000,
                "comment": "Close position",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # 發送平倉請求
            result = mt5.order_send(request)
            if result is None:
                print(f"❌ 平倉失敗: order_send 返回 None")
                print(f"   最後錯誤: {mt5.last_error()}")
                return None
            
            print(f"📊 平倉回應: retcode={result.retcode}, comment={result.comment}")
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                print(f"❌ 平倉失敗: {result.comment} (retcode: {result.retcode})")
                return None
            
            return {
                "order": result.order,
                "volume": result.volume,
                "price": result.price,
                "retcode": result.retcode,
                "comment": result.comment
            }
            
        except Exception as e:
            print(f"❌ 平倉錯誤: {e}")
            return None
    
    def get_positions(self, symbol: str = None) -> List[Dict]:
        """獲取持倉列表"""
        if not self.connected:
            print("❌ MT5 未連接")
            return []
        
        try:
            if symbol:
                positions = mt5.positions_get(symbol=symbol)
            else:
                positions = mt5.positions_get()
            
            if positions is None:
                return []
            
            position_list = []
            for position in positions:
                position_list.append({
                    "ticket": position.ticket,
                    "symbol": position.symbol,
                    "type": position.type,  # 0=Buy, 1=Sell
                    "volume": position.volume,
                    "price_open": position.price_open,
                    "price_current": position.price_current,
                    "profit": position.profit,
                    "swap": position.swap,
                    "time": datetime.fromtimestamp(position.time)
                })
            
            return position_list
            
        except Exception as e:
            print(f"❌ 獲取持倉列表錯誤: {e}")
            return []
    
    def get_account_info(self) -> Optional[Dict]:
        """獲取帳戶資訊"""
        if not self.connected:
            print("❌ MT5 未連接")
            return None
        
        try:
            account_info = mt5.account_info()
            if account_info is None:
                print("❌ 無法獲取帳戶資訊")
                return None
            
            return {
                "login": account_info.login,
                "balance": account_info.balance,
                "equity": account_info.equity,
                "margin": account_info.margin,
                "margin_free": account_info.margin_free,
                "profit": account_info.profit,
                "currency": account_info.currency
            }
            
        except Exception as e:
            print(f"❌ 獲取帳戶資訊錯誤: {e}")
            return None
    
    def calculate_position_size(self, account_balance: float, leverage: int = 10, price: float = None) -> float:
        """計算持倉大小"""
        if price is None:
            tick = self.get_tick("XAUUSD+")
            if tick:
                price = (tick['bid'] + tick['ask']) / 2
            else:
                return 0
        
        # 使用 10% 的帳戶餘額
        available_balance = account_balance * 0.1
        position_value = available_balance * leverage
        position_size = position_value / price
        
        return round(position_size, 2)
    
    def test_connection(self) -> bool:
        """測試連接"""
        print("🔍 測試 Bybit MT5 連接...")
        
        if not self.connect():
            return False
        
        # 測試帳戶資訊
        account_info = self.get_account_info()
        if account_info:
            print("✅ 帳戶資訊獲取成功")
            print(f"   帳號: {account_info['login']}")
            print(f"   餘額: {account_info['balance']:.2f} {account_info['currency']}")
            print(f"   權益: {account_info['equity']:.2f} {account_info['currency']}")
            print(f"   可用保證金: {account_info['margin_free']:.2f} {account_info['currency']}")
        
        # 測試獲取交易對列表
        symbols = self.get_available_symbols()
        if symbols:
            print(f"✅ 可用交易對數量: {len(symbols)}")
            print(f"📋 前10個交易對: {symbols[:10]}")
        
        # 測試 XAUUSD+ 報價
        xauusd = self.get_tick("XAUUSD+")
        if xauusd:
            print(f"✅ XAUUSD+ 報價測試成功")
            print(f"   買價: {xauusd['bid']}")
            print(f"   賣價: {xauusd['ask']}")
            print(f"   最新價: {xauusd['last']}")
            print(f"   點差: {xauusd['spread']}")
        else:
            print("❌ XAUUSD+ 報價測試失敗")
        
        # 測試持倉資訊
        positions = self.get_positions()
        if positions:
            print(f"✅ 持倉資訊獲取成功，共 {len(positions)} 個持倉")
            for pos in positions[:3]:  # 只顯示前3個
                print(f"   {pos['symbol']}: {pos['volume']} 手 ({'買入' if pos['type'] == 0 else '賣出'})")
        else:
            print("ℹ️ 目前無持倉")
        
        return True

# 測試函數
if __name__ == "__main__":
    client = BybitMT5Client()
    
    # 測試連接
    if client.test_connection():
        print("\n🎉 Bybit MT5 客戶端測試成功！")
    else:
        print("\n❌ Bybit MT5 客戶端測試失敗")
    
    # 清理連接
    client.disconnect() 