import os
import requests
import time
import hmac
import hashlib
from urllib.parse import urlencode
from dotenv import load_dotenv

load_dotenv()

class BybitPositionChecker:
    def __init__(self):
        self.api_key = os.getenv('BYBIT_API_KEY')
        self.api_secret = os.getenv('BYBIT_API_SECRET')
        self.base_url = "https://api.bybit.com"
        
    def _generate_signature(self, timestamp, recv_window, params=None):
        param_str = ""
        if params:
            param_str = urlencode(params)
        
        param_str = f"{timestamp}{self.api_key}{recv_window}{param_str}"
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            param_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def get_wallet_balance(self):
        """獲取錢包餘額"""
        endpoint = "/v5/account/wallet-balance"
        params = {
            "accountType": "UNIFIED",
            "recvWindow": 10000
        }
        
        timestamp = str(int(time.time() * 1000))
        signature = self._generate_signature(timestamp, 10000, params)
        
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-TIMESTAMP": timestamp,
            "X-BAPI-SIGN": signature,
            "X-BAPI-RECV-WINDOW": "10000"
        }
        
        url = self.base_url + endpoint
        response = requests.get(url, headers=headers, params=params)
        return response.json()
    
    def get_positions(self):
        """獲取持倉資訊"""
        endpoint = "/v5/position/list"
        params = {
            "category": "linear",
            "recvWindow": 10000
        }
        
        timestamp = str(int(time.time() * 1000))
        signature = self._generate_signature(timestamp, 10000, params)
        
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-TIMESTAMP": timestamp,
            "X-BAPI-SIGN": signature,
            "X-BAPI-RECV-WINDOW": "10000"
        }
        
        url = self.base_url + endpoint
        response = requests.get(url, headers=headers, params=params)
        return response.json()
    
    def get_open_orders(self):
        """獲取未成交訂單"""
        endpoint = "/v5/order/realtime"
        params = {
            "category": "linear",
            "recvWindow": 10000
        }
        
        timestamp = str(int(time.time() * 1000))
        signature = self._generate_signature(timestamp, 10000, params)
        
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-TIMESTAMP": timestamp,
            "X-BAPI-SIGN": signature,
            "X-BAPI-RECV-WINDOW": "10000"
        }
        
        url = self.base_url + endpoint
        response = requests.get(url, headers=headers, params=params)
        return response.json()

def main():
    print("🔍 Bybit 資金狀況檢查...")
    print("=" * 50)
    
    checker = BybitPositionChecker()
    
    # 檢查API密鑰
    if not checker.api_key or not checker.api_secret:
        print("❌ API密鑰未正確載入")
        print(f"API Key: {checker.api_key}")
        print(f"API Secret: {checker.api_secret[:10] if checker.api_secret else 'None'}...")
        return
    
    # 1. 檢查錢包餘額
    print("\n1. 錢包餘額檢查:")
    balance_data = checker.get_wallet_balance()
    if balance_data.get('retCode') == 0:
        result = balance_data.get('result', {})
        list_data = result.get('list', [])
        if list_data:
            account = list_data[0]
            print(f"[API原始回傳] accountType: {account.get('accountType')}")
            print(f"[API原始回傳] totalWalletBalance: {account.get('totalWalletBalance')}")
            print(f"[API原始回傳] totalAvailableBalance: {account.get('totalAvailableBalance')}")
            print(f"[API原始回傳] totalMarginBalance: {account.get('totalMarginBalance')}")
            print(f"[API原始回傳] totalInitialMargin: {account.get('totalInitialMargin')}")
            print(f"[API原始回傳] totalEquity: {account.get('totalEquity')}")
            print(f"[API原始回傳] totalAvailableBalance: {account.get('totalAvailableBalance')} (正確可用下單金額)")
            print(f"[API原始回傳] coin: {account.get('coin')}")
            # 顯示USDT細節
            usdt = None
            for c in account.get('coin', []):
                if c.get('coin') == 'USDT':
                    usdt = c
                    break
            if usdt:
                print("\n[USDT細節]")
                for k, v in usdt.items():
                    print(f"   {k}: {v}")
                print(f"\n[重點] USDT walletBalance: {usdt.get('walletBalance')} (即App顯示錢包餘額)")
                print(f"[重點] USDT equity: {usdt.get('equity')}")
                print(f"[重點] USDT totalAvailableBalance: {usdt.get('walletBalance')} (正確可用下單金額)")
            print(f"\n[重點] totalAvailableBalance: {account.get('totalAvailableBalance')} (統一帳戶可用下單金額，應與App一致)")
        else:
            print("   ❌ 無帳戶資料")
    else:
        print(f"   ❌ 獲取餘額失敗: {balance_data}")
    
    # 2. 檢查持倉
    print("\n2. 當前持倉:")
    positions_data = checker.get_positions()
    if positions_data.get('retCode') == 0:
        positions = positions_data.get('result', {}).get('list', [])
        if positions:
            for pos in positions:
                if float(pos.get('size', 0)) > 0:
                    symbol = pos.get('symbol')
                    size = float(pos.get('size', 0))
                    side = pos.get('side')
                    avg_price = float(pos.get('avgPrice', 0))
                    unrealized_pnl = float(pos.get('unrealisedPnl', 0))
                    margin = float(pos.get('initialMargin', 0))
                    
                    print(f"   📊 {symbol} {side}")
                    print(f"      數量: {size}")
                    print(f"      均價: {avg_price}")
                    print(f"      未實現損益: {unrealized_pnl}")
                    print(f"      保證金: {margin}")
        else:
            print("   ✅ 無持倉")
    else:
        print(f"   ❌ 獲取持倉失敗: {positions_data}")
    
    # 3. 檢查未成交訂單
    print("\n3. 未成交訂單:")
    orders_data = checker.get_open_orders()
    if orders_data.get('retCode') == 0:
        orders = orders_data.get('result', {}).get('list', [])
        if orders:
            for order in orders:
                symbol = order.get('symbol')
                side = order.get('side')
                qty = float(order.get('qty', 0))
                price = float(order.get('price', 0))
                order_type = order.get('orderType')
                
                print(f"   📋 {symbol} {side} {order_type}")
                print(f"      數量: {qty}, 價格: {price}")
        else:
            print("   ✅ 無未成交訂單")
    else:
        print(f"   ❌ 獲取訂單失敗: {orders_data}")
    
    print("\n" + "=" * 50)
    print("💡 建議:")
    print("1. 如果有持倉，請考慮平倉釋放保證金")
    print("2. 如果有未成交訂單，請取消它們")
    print("3. 或者充值更多資金到帳戶")

if __name__ == "__main__":
    main() 