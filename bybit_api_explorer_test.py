#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import hmac
import hashlib
import json
import requests
from dotenv import load_dotenv

load_dotenv('config.env')

def bybit_api_explorer_test():
    api_key = os.getenv('BYBIT_API_KEY')
    api_secret = os.getenv('BYBIT_API_SECRET')
    base_url = "https://api.bybit.com"
    endpoint = "/v5/order/create"
    recv_window = 10000
    timestamp = str(int(time.time() * 1000))
    
    # Explorer預設參數
    params = {
        "category": "linear",
        "symbol": "XAUTUSDT",
        "side": "Buy",
        "orderType": "Market",
        "qty": "0.01",
        "timeInForce": "GTC",
        "recvWindow": recv_window
    }
    body = json.dumps(params, separators=(",", ":"), ensure_ascii=False)
    origin_string = timestamp + api_key + str(recv_window) + body
    signature = hmac.new(api_secret.encode(), origin_string.encode(), hashlib.sha256).hexdigest()
    headers = {
        "X-BAPI-API-KEY": api_key,
        "X-BAPI-TIMESTAMP": timestamp,
        "X-BAPI-SIGN": signature,
        "X-BAPI-RECV-WINDOW": str(recv_window),
        "Content-Type": "application/json"
    }
    url = base_url + endpoint
    print("=== Bybit API Explorer 測試 ===")
    print(f"POST {url}")
    print(f"Headers: {headers}")
    print(f"Body: {body}")
    print(f"Origin String: {origin_string}")
    print(f"Signature: {signature}")
    try:
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        print(f"Status Code: {resp.status_code}")
        print(f"Response: {resp.text}")
    except Exception as e:
        print(f"請求失敗: {e}")

if __name__ == "__main__":
    bybit_api_explorer_test() 