#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能套利策略 - 基于历史数据分析的动态阈值系统
根据xau_spread_data.csv的统计分析设计的优化策略
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pytz

class SmartArbitrageStrategy:
    """智能套利策略类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 基于数据分析的策略参数
        self.strategy_config = {
            # 基础统计数据 (来自xau_spread_data.csv分析)
            'avg_spread': 0.1098,      # 平均价差 0.1098%
            'median_spread': 0.1182,   # 中位数价差 0.1182%
            'std_spread': 0.0577,      # 标准差 0.0577%
            'max_spread': 0.3372,      # 最大价差 0.3372%
            
            # 阈值分布
            'spread_gt_03_pct': 0.07,  # >0.3%的数据点仅0.07%
            'spread_gt_025_pct': 0.51, # >0.25%的数据点0.51%
            'spread_gt_02_pct': 4.11,  # >0.2%的数据点4.11%
            'spread_lt_01_pct': 40.11, # <0.1%的数据点40.11%
            
            # 最佳套利窗口 (周一早上6点GMT+3)
            'best_window': {
                'weekday': 0,  # Monday
                'hour_start': 6,
                'hour_end': 7,
                'avg_spread': 0.2017,
                'max_spread': 0.3372,
                'opportunities_gt_03': 4  # 4次>0.3%机会
            }
        }
        
        # 动态阈值策略
        self.dynamic_thresholds = self._calculate_dynamic_thresholds()
        
        # 时间窗口策略
        self.time_windows = self._define_time_windows()
        
        self.logger.info("智能套利策略初始化完成")
        self.logger.info(f"动态阈值: {self.dynamic_thresholds}")
    
    def _calculate_dynamic_thresholds(self) -> Dict:
        """基于统计数据计算动态阈值"""
        avg = self.strategy_config['avg_spread']
        std = self.strategy_config['std_spread']
        
        return {
            # 保守策略 (高概率，低风险)
            'conservative': {
                'entry': avg + 2 * std,    # 0.1098 + 2*0.0577 = 0.2252%
                'exit': avg,               # 0.1098%
                'description': '保守策略：进场0.225%，出场0.110%'
            },
            
            # 平衡策略 (中等概率，中等风险)
            'balanced': {
                'entry': avg + 2.5 * std,  # 0.1098 + 2.5*0.0577 = 0.2541%
                'exit': avg - 0.5 * std,   # 0.1098 - 0.5*0.0577 = 0.0809%
                'description': '平衡策略：进场0.254%，出场0.081%'
            },
            
            # 激进策略 (低概率，高收益)
            'aggressive': {
                'entry': 0.30,            # 0.30% (仅0.07%概率)
                'exit': avg + std,         # 0.1098 + 0.0577 = 0.1675%
                'description': '激进策略：进场0.300%，出场0.168%'
            },
            
            # 黄金时间策略 (周一早上6点专用)
            'golden_hour': {
                'entry': 0.25,            # 0.25% (周一6点平均0.2017%)
                'exit': 0.15,             # 0.15% (快速出场)
                'description': '黄金时间策略：进场0.250%，出场0.150%'
            }
        }
    
    def _define_time_windows(self) -> Dict:
        """定义时间窗口策略"""
        return {
            # 黄金时间窗口 (周一早上6-7点GMT+3)
            'golden_hour': {
                'weekday': 0,  # Monday
                'hour_start': 6,
                'hour_end': 7,
                'strategy': 'golden_hour',
                'max_positions': 4,  # 最多4个仓位
                'description': '周一早上6-7点：最佳套利窗口'
            },
            
            # 高活跃时间 (周一-周五 0-1点, 12-17点GMT+3)
            'high_activity': {
                'weekdays': [0, 1, 2, 3, 4],
                'hours': [0, 12, 13, 14, 15, 16],
                'strategy': 'balanced',
                'max_positions': 2,
                'description': '高活跃时间：平衡策略'
            },
            
            # 低活跃时间 (其他时间)
            'low_activity': {
                'strategy': 'conservative',
                'max_positions': 1,
                'description': '低活跃时间：保守策略'
            }
        }
    
    def get_current_strategy(self) -> Tuple[str, Dict]:
        """获取当前时间应该使用的策略"""
        # 获取GMT+3时间
        gmt3_tz = pytz.timezone('Europe/Moscow')
        now_gmt3 = datetime.now(gmt3_tz)
        weekday = now_gmt3.weekday()
        hour = now_gmt3.hour
        
        # 检查是否在黄金时间窗口
        golden = self.time_windows['golden_hour']
        if (weekday == golden['weekday'] and 
            golden['hour_start'] <= hour < golden['hour_end']):
            strategy_name = golden['strategy']
            return strategy_name, self.dynamic_thresholds[strategy_name]
        
        # 检查是否在高活跃时间
        high_activity = self.time_windows['high_activity']
        if (weekday in high_activity['weekdays'] and 
            hour in high_activity['hours']):
            strategy_name = high_activity['strategy']
            return strategy_name, self.dynamic_thresholds[strategy_name]
        
        # 默认使用低活跃时间策略
        strategy_name = self.time_windows['low_activity']['strategy']
        return strategy_name, self.dynamic_thresholds[strategy_name]
    
    def should_enter_trade(self, spread_percentage: float) -> Tuple[bool, str]:
        """判断是否应该进场"""
        strategy_name, thresholds = self.get_current_strategy()
        entry_threshold = thresholds['entry']
        
        abs_spread = abs(spread_percentage)
        should_enter = abs_spread >= entry_threshold
        
        reason = f"策略:{strategy_name}, 价差:{abs_spread:.4f}%, 阈值:{entry_threshold:.4f}%"
        
        return should_enter, reason
    
    def should_exit_trade(self, spread_percentage: float, entry_strategy: str = None) -> Tuple[bool, str]:
        """判断是否应该出场"""
        # 如果有进场策略记录，使用相同策略的出场阈值
        if entry_strategy and entry_strategy in self.dynamic_thresholds:
            thresholds = self.dynamic_thresholds[entry_strategy]
        else:
            # 否则使用当前策略
            _, thresholds = self.get_current_strategy()
        
        exit_threshold = thresholds['exit']
        abs_spread = abs(spread_percentage)
        should_exit = abs_spread <= exit_threshold
        
        reason = f"价差:{abs_spread:.4f}%, 出场阈值:{exit_threshold:.4f}%"
        
        return should_exit, reason
    
    def get_max_positions(self) -> int:
        """获取当前时间窗口的最大仓位数"""
        gmt3_tz = pytz.timezone('Europe/Moscow')
        now_gmt3 = datetime.now(gmt3_tz)
        weekday = now_gmt3.weekday()
        hour = now_gmt3.hour
        
        # 检查黄金时间窗口
        golden = self.time_windows['golden_hour']
        if (weekday == golden['weekday'] and 
            golden['hour_start'] <= hour < golden['hour_end']):
            return golden['max_positions']
        
        # 检查高活跃时间
        high_activity = self.time_windows['high_activity']
        if (weekday in high_activity['weekdays'] and 
            hour in high_activity['hours']):
            return high_activity['max_positions']
        
        # 默认低活跃时间
        return self.time_windows['low_activity']['max_positions']
    
    def get_strategy_info(self) -> Dict:
        """获取当前策略信息"""
        strategy_name, thresholds = self.get_current_strategy()
        max_positions = self.get_max_positions()
        
        gmt3_tz = pytz.timezone('Europe/Moscow')
        now_gmt3 = datetime.now(gmt3_tz)
        
        return {
            'current_time_gmt3': now_gmt3.strftime('%Y-%m-%d %H:%M:%S'),
            'strategy_name': strategy_name,
            'entry_threshold': thresholds['entry'],
            'exit_threshold': thresholds['exit'],
            'max_positions': max_positions,
            'description': thresholds['description']
        }
    
    def analyze_historical_performance(self) -> Dict:
        """分析历史表现（基于已知数据）"""
        return {
            'data_period': '2025-07-03 to 2025-07-09',
            'total_data_points': 6036,
            'avg_spread': f"{self.strategy_config['avg_spread']:.4f}%",
            'opportunities': {
                'conservative': f"{self.strategy_config['spread_gt_02_pct']:.2f}%",
                'balanced': f"{self.strategy_config['spread_gt_025_pct']:.2f}%",
                'aggressive': f"{self.strategy_config['spread_gt_03_pct']:.2f}%"
            },
            'best_window': {
                'time': '周一早上6-7点GMT+3',
                'avg_spread': f"{self.strategy_config['best_window']['avg_spread']:.4f}%",
                'max_spread': f"{self.strategy_config['best_window']['max_spread']:.4f}%",
                'opportunities': f"{self.strategy_config['best_window']['opportunities_gt_03']}次>0.3%"
            },
            'exit_opportunities': f"{self.strategy_config['spread_lt_01_pct']:.2f}%"
        }

# 使用示例
if __name__ == "__main__":
    # 创建智能策略
    strategy = SmartArbitrageStrategy()
    
    # 获取当前策略信息
    info = strategy.get_strategy_info()
    print("=== 当前策略信息 ===")
    for key, value in info.items():
        print(f"{key}: {value}")
    
    # 测试进场判断
    test_spreads = [0.15, 0.20, 0.25, 0.30, 0.35]
    print("\n=== 进场判断测试 ===")
    for spread in test_spreads:
        should_enter, reason = strategy.should_enter_trade(spread)
        print(f"价差{spread:.2f}%: {'✅进场' if should_enter else '❌等待'} - {reason}")
    
    # 分析历史表现
    performance = strategy.analyze_historical_performance()
    print("\n=== 历史表现分析 ===")
    for key, value in performance.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for sub_key, sub_value in value.items():
                print(f"  {sub_key}: {sub_value}")
        else:
            print(f"{key}: {value}")
