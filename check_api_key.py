#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from dotenv import load_dotenv

load_dotenv('config.env')

def check_api_key():
    """檢查 API Key 的完整性和格式"""
    
    api_key = os.getenv('BYBIT_API_KEY')
    api_secret = os.getenv('BYBIT_API_SECRET')
    
    print("=== API Key 檢查 ===")
    print(f"API Key: '{api_key}'")
    print(f"API Secret: '{api_secret[:10]}...'")
    print(f"API Key 長度: {len(api_key) if api_key else 0}")
    print(f"API Secret 長度: {len(api_secret) if api_secret else 0}")
    print()
    
    # 檢查是否有空格或換行符
    if api_key:
        print(f"API Key 包含空格: {'是' if ' ' in api_key else '否'}")
        print(f"API Key 包含換行符: {'是' if '\n' in api_key else '否'}")
        print(f"API Key 包含回車符: {'是' if '\r' in api_key else '否'}")
        print(f"API Key 去除空白後: '{api_key.strip()}'")
        print(f"去除空白後長度: {len(api_key.strip())}")
    
    if api_secret:
        print(f"API Secret 包含空格: {'是' if ' ' in api_secret else '否'}")
        print(f"API Secret 包含換行符: {'是' if '\n' in api_secret else '否'}")
        print(f"API Secret 包含回車符: {'是' if '\r' in api_secret else '否'}")
        print(f"API Secret 去除空白後: '{api_secret.strip()[:10]}...'")
        print(f"去除空白後長度: {len(api_secret.strip())}")
    
    print()
    print("=== 建議 ===")
    print("1. 正常的 Bybit API Key 通常長度在 20-50 字符之間")
    print("2. 正常的 Bybit API Secret 通常長度在 30-100 字符之間")
    print("3. 請檢查你的 API Key 是否完整複製")
    print("4. 請檢查 config.env 文件中是否有換行符或空格")

if __name__ == "__main__":
    check_api_key() 