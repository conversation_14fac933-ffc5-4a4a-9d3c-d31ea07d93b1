#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的阈值设置
进场: 0.2%
出场: 0.05%
熔断: 0.5%
时区: 东八区 (UTC+8)
"""

import sys
import logging
from datetime import datetime
import pytz

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_market_timing_utc8():
    """测试东八区市场时间检测"""
    print("=" * 60)
    print("🕐 市场时间检测测试 (东八区 UTC+8)")
    print("=" * 60)
    
    # 获取东八区时间
    utc8_tz = pytz.timezone('Asia/Shanghai')
    now_utc8 = datetime.now(utc8_tz)
    weekday = now_utc8.weekday()
    hour = now_utc8.hour
    minute = now_utc8.minute
    current_time = hour * 60 + minute
    
    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    
    print(f"当前东八区时间: {now_utc8.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"星期: {weekday_names[weekday]}")
    print(f"小时: {hour}, 分钟: {minute}")
    
    # 判断市场状态 (根据Bybit MT5时间转换为东八区)
    market_open = False
    
    if weekday == 6:  # Sunday
        market_open = False
        status = "周日休市"
    elif weekday == 5:  # Saturday
        # 原00:00-04:57 GMT+3 = 05:00-09:57 UTC+8
        if (5 * 60) <= current_time <= (9 * 60 + 57):
            market_open = True
            status = "周六交易时间 (05:00-09:57)"
        else:
            market_open = False
            status = "周六休市时间"
    else:  # Monday-Friday
        # 原06:00-23:59 GMT+3 = 11:00-04:59+1 UTC+8
        # 原00:00-04:58 GMT+3 = 05:00-09:58 UTC+8
        if (current_time >= 11 * 60) or (current_time <= (9 * 60 + 58)):
            market_open = True
            if current_time >= 11 * 60:
                status = "工作日交易时间 (11:00-04:59+1)"
            else:
                status = "工作日交易时间 (05:00-09:58)"
        else:
            market_open = False
            status = "工作日休市时间 (10:00-10:59)"
    
    print(f"市场状态: {'🟢 开放' if market_open else '🔴 关闭'}")
    print(f"状态描述: {status}")
    
    return market_open

def test_new_thresholds():
    """测试新的阈值设置"""
    print("\n" + "=" * 60)
    print("🎯 新阈值设置测试")
    print("=" * 60)
    
    # 新的阈值设置
    entry_threshold = 0.2    # 进场: 0.2%
    exit_threshold = 0.05    # 出场: 0.05%
    risk_threshold = 0.5     # 熔断: 0.5%
    
    print(f"📊 阈值设置:")
    print(f"   进场阈值: {entry_threshold}%")
    print(f"   出场阈值: {exit_threshold}%")
    print(f"   熔断阈值: {risk_threshold}%")
    
    print(f"\n📈 进场测试:")
    test_spreads = [0.05, 0.10, 0.15, 0.18, 0.20, 0.25, 0.30, 0.35, 0.40, 0.50, 0.60]
    for spread in test_spreads:
        should_enter = abs(spread) >= entry_threshold
        should_risk = abs(spread) >= risk_threshold
        
        if should_risk:
            status = "🚨 熔断"
        elif should_enter:
            status = "✅ 进场"
        else:
            status = "❌ 等待"
        
        print(f"   价差 {spread:.2f}%: {status}")
    
    print(f"\n📉 出场测试:")
    exit_spreads = [0.01, 0.03, 0.05, 0.06, 0.08, 0.10, 0.15, 0.20]
    for spread in exit_spreads:
        should_exit = abs(spread) <= exit_threshold
        status = "✅ 出场" if should_exit else "❌ 持有"
        print(f"   价差 {spread:.2f}%: {status}")

def analyze_historical_with_new_thresholds():
    """用新阈值分析历史数据"""
    print("\n" + "=" * 60)
    print("📊 历史数据分析 (新阈值)")
    print("=" * 60)
    
    try:
        import pandas as pd
        
        # 读取历史数据
        df = pd.read_csv('xau_spread_data.csv')
        df['abs_percentage'] = abs(df['percentage_spread'])
        
        total_points = len(df)
        entry_opportunities = len(df[df['abs_percentage'] >= 0.2])
        exit_opportunities = len(df[df['abs_percentage'] <= 0.05])
        risk_events = len(df[df['abs_percentage'] >= 0.5])
        
        print(f"📈 历史数据分析:")
        print(f"   总数据点: {total_points}")
        print(f"   进场机会 (≥0.2%): {entry_opportunities} ({entry_opportunities/total_points*100:.2f}%)")
        print(f"   出场机会 (≤0.05%): {exit_opportunities} ({exit_opportunities/total_points*100:.2f}%)")
        print(f"   熔断事件 (≥0.5%): {risk_events} ({risk_events/total_points*100:.2f}%)")
        
        # 分析进场后的出场时间
        entry_points = df[df['abs_percentage'] >= 0.2].copy()
        print(f"\n⏰ 进场分析:")
        print(f"   进场机会总数: {len(entry_points)}")
        
        if len(entry_points) > 0:
            print(f"   平均进场价差: {entry_points['abs_percentage'].mean():.4f}%")
            print(f"   最大进场价差: {entry_points['abs_percentage'].max():.4f}%")
            print(f"   最小进场价差: {entry_points['abs_percentage'].min():.4f}%")
        
        # 分析出场机会
        exit_points = df[df['abs_percentage'] <= 0.05].copy()
        print(f"\n🚪 出场分析:")
        print(f"   出场机会总数: {len(exit_points)}")
        
        if len(exit_points) > 0:
            print(f"   平均出场价差: {exit_points['abs_percentage'].mean():.4f}%")
            print(f"   最大出场价差: {exit_points['abs_percentage'].max():.4f}%")
            print(f"   最小出场价差: {exit_points['abs_percentage'].min():.4f}%")
        
        # 风险分析
        if risk_events > 0:
            risk_points = df[df['abs_percentage'] >= 0.5].copy()
            print(f"\n⚠️ 风险分析:")
            print(f"   熔断事件: {risk_events}次")
            print(f"   最大风险价差: {risk_points['abs_percentage'].max():.4f}%")
            print(f"   平均风险价差: {risk_points['abs_percentage'].mean():.4f}%")
        else:
            print(f"\n✅ 风险分析: 历史数据中无熔断事件")
        
    except Exception as e:
        print(f"❌ 无法分析历史数据: {e}")

def main():
    """主测试函数"""
    print("🚀 新阈值系统测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试市场时间检测 (东八区)
        market_open = test_market_timing_utc8()
        
        # 2. 测试新阈值
        test_new_thresholds()
        
        # 3. 分析历史数据
        analyze_historical_with_new_thresholds()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成")
        print("=" * 60)
        
        # 总结
        print("\n📋 测试总结:")
        print(f"   市场状态: {'开放' if market_open else '关闭'}")
        print(f"   进场阈值: 0.2%")
        print(f"   出场阈值: 0.05%")
        print(f"   熔断阈值: 0.5%")
        print(f"   时区设置: 东八区 (UTC+8)")
        
        print("\n🎯 关键改进:")
        print("   ✅ 修正时区为东八区 (UTC+8)")
        print("   ✅ 降低进场阈值至 0.2%")
        print("   ✅ 大幅降低出场阈值至 0.05%")
        print("   ✅ 降低熔断阈值至 0.5%")
        print("   ✅ 保持原有杠杆和仓位管理")
        
        print("\n💡 预期效果:")
        print("   - 更多进场机会 (0.2% vs 0.3%)")
        print("   - 更快出场速度 (0.05% vs 0.1%)")
        print("   - 更严格风控 (0.5% vs 0.7%)")
        print("   - 减少持仓时间和费用成本")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
