#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新策略测试脚本
--------------
测试新的套利策略参数和功能
"""

from xau_arbitrage_trader import XAUArbitrageTrader
from datetime import datetime

def test_strategy_parameters():
    """测试策略参数"""
    print("🧪 测试新策略参数...")
    
    trader = XAUArbitrageTrader()
    
    # 验证新参数
    print(f"✅ 进场阈值: {trader.min_spread_threshold}% (应为 0.3%)")
    print(f"✅ 出场阈值: {trader.close_spread_threshold}% (应为 0.1%)")
    print(f"✅ 风控阈值: {trader.risk_spread_threshold}% (应为 0.7%)")
    print(f"✅ 单次资金比例: {trader.position_size_ratio}% (应为 0.02 = 2%)")
    print(f"✅ 最大仓位数: {trader.max_positions} (应为 5)")
    print(f"✅ 最大总曝险: {trader.max_total_exposure}% (应为 0.2 = 20%)")
    print(f"✅ 杠杆倍数: {trader.leverage}x (应为 20x)")
    
    return True

def test_arbitrage_conditions():
    """测试套利条件检查"""
    print("\n🧪 测试套利条件检查...")

    trader = XAUArbitrageTrader()

    # 测试周末检测功能
    print("🧪 测试周末检测功能...")
    weekend_result = trader.check_weekend_status()
    from datetime import datetime
    now = datetime.now()
    weekday = now.weekday()
    is_weekend = weekday in [5, 6]
    expected_weekend = not is_weekend

    if weekend_result == expected_weekend:
        print("✅ 周末检测功能正常")
    else:
        print(f"❌ 周末检测异常 - 当前: {weekend_result}, 预期: {expected_weekend}")

    # 测试正常条件
    spread_info = {'percentage': 0.35}  # 0.35% > 0.3%
    funding_rate = -0.001

    result = trader.check_arbitrage_conditions(spread_info, funding_rate)
    print(f"✅ 正常条件测试 (0.35%价差): {result} (应为 True)")
    
    # 测试价差不足
    spread_info = {'percentage': 0.25}  # 0.25% < 0.3%
    result = trader.check_arbitrage_conditions(spread_info, funding_rate)
    print(f"✅ 价差不足测试 (0.25%价差): {result} (应为 False)")
    
    # 测试最大仓位限制
    trader.active_trades = {f"test_{i}": {} for i in range(5)}  # 模拟5个活跃交易
    spread_info = {'percentage': 0.35}
    result = trader.check_arbitrage_conditions(spread_info, funding_rate)
    print(f"✅ 最大仓位限制测试: {result} (应为 False)")
    
    return True

def test_risk_control():
    """测试风控功能"""
    print("\n🧪 测试风控功能...")
    
    trader = XAUArbitrageTrader()
    
    # 测试风控条件
    spread_info = {'percentage': 0.8}  # 0.8% > 0.7%
    result = trader.check_risk_control(spread_info)
    print(f"✅ 风控条件测试 (0.8%价差): {result} (应为 True)")
    
    spread_info = {'percentage': 0.5}  # 0.5% < 0.7%
    result = trader.check_risk_control(spread_info)
    print(f"✅ 正常价差测试 (0.5%价差): {result} (应为 False)")
    
    return True

def test_close_conditions():
    """测试平仓条件"""
    print("\n🧪 测试平仓条件...")
    
    trader = XAUArbitrageTrader()
    
    # 测试平仓条件
    spread_info = {'percentage': 0.05}  # 0.05% < 0.1%
    result = trader.check_close_conditions(spread_info)
    print(f"✅ 平仓条件测试 (0.05%价差): {result} (应为 True)")
    
    spread_info = {'percentage': 0.15}  # 0.15% > 0.1%
    result = trader.check_close_conditions(spread_info)
    print(f"✅ 继续持仓测试 (0.15%价差): {result} (应为 False)")
    
    return True

def test_position_sizing():
    """测试仓位计算"""
    print("\n🧪 测试仓位计算...")
    
    trader = XAUArbitrageTrader()
    
    # 模拟价格
    bybit_price = 2000.0
    mt5_price = 2001.0
    
    print("📊 仓位计算逻辑:")
    print(f"   - 以较小账户余额为基准")
    print(f"   - 每次使用 {trader.position_size_ratio*100}% 资金作保证金")
    print(f"   - {trader.leverage}倍杠杆")
    print(f"   - 确保两边名义价值和保证金都相等")
    
    return True

def main():
    """主测试函数"""
    print("🚀 新套利策略测试开始")
    print("=" * 50)
    
    try:
        # 运行所有测试
        test_strategy_parameters()
        test_arbitrage_conditions()
        test_risk_control()
        test_close_conditions()
        test_position_sizing()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")
        print("\n📋 新策略要点总结:")
        print("1. 进场阈值: 0.3% (降低门槛)")
        print("2. 出场阈值: 0.1% (更快止盈)")
        print("3. 风控阈值: 0.7% (异常价差保护)")
        print("4. 每次使用2%资金，20倍杠杆")
        print("5. 最多5个并发仓位")
        print("6. 每分钟检测，符合条件就开仓")
        print("7. 全面的Telegram通知")
        print("8. 24小时风控冷却期")
        print("9. MT5市场开放检测")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
