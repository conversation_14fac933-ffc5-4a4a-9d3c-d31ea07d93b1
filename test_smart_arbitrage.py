#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能套利系统测试脚本
测试新的智能策略和市场时间检测
"""

import sys
import logging
from datetime import datetime
import pytz
from smart_arbitrage_strategy import SmartArbitrageStrategy

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_market_timing():
    """测试市场时间检测"""
    print("=" * 60)
    print("🕐 市场时间检测测试")
    print("=" * 60)
    
    # 获取GMT+3时间
    gmt3_tz = pytz.timezone('Europe/Moscow')
    now_gmt3 = datetime.now(gmt3_tz)
    weekday = now_gmt3.weekday()
    hour = now_gmt3.hour
    minute = now_gmt3.minute
    current_time = hour * 60 + minute
    
    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    
    print(f"当前GMT+3时间: {now_gmt3.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"星期: {weekday_names[weekday]}")
    print(f"小时: {hour}, 分钟: {minute}")
    
    # 判断市场状态
    market_open = False
    
    if weekday == 6:  # Sunday
        market_open = False
        status = "周日休市"
    elif weekday == 5:  # Saturday
        if current_time <= (4 * 60 + 57):
            market_open = True
            status = "周六交易时间 (00:00-04:57)"
        else:
            market_open = False
            status = "周六休市时间"
    else:  # Monday-Friday
        if (current_time >= 6 * 60) or (current_time <= (4 * 60 + 58)):
            market_open = True
            if current_time >= 6 * 60:
                status = "工作日交易时间 (06:00-23:59)"
            else:
                status = "工作日交易时间 (00:00-04:58)"
        else:
            market_open = False
            status = "工作日休市时间 (05:00-05:59)"
    
    print(f"市场状态: {'🟢 开放' if market_open else '🔴 关闭'}")
    print(f"状态描述: {status}")
    
    return market_open

def test_smart_strategy():
    """测试智能策略"""
    print("\n" + "=" * 60)
    print("🧠 智能策略测试")
    print("=" * 60)
    
    strategy = SmartArbitrageStrategy()
    
    # 获取当前策略信息
    info = strategy.get_strategy_info()
    print("📊 当前策略信息:")
    for key, value in info.items():
        print(f"   {key}: {value}")
    
    print("\n📈 进场测试:")
    test_spreads = [0.10, 0.15, 0.20, 0.225, 0.25, 0.30, 0.35]
    for spread in test_spreads:
        should_enter, reason = strategy.should_enter_trade(spread)
        status = "✅ 进场" if should_enter else "❌ 等待"
        print(f"   价差 {spread:.3f}%: {status} - {reason}")
    
    print("\n📉 出场测试:")
    exit_spreads = [0.05, 0.08, 0.10, 0.11, 0.15, 0.20]
    for spread in exit_spreads:
        should_exit, reason = strategy.should_exit_trade(spread)
        status = "✅ 出场" if should_exit else "❌ 持有"
        print(f"   价差 {spread:.3f}%: {status} - {reason}")
    
    print(f"\n📦 最大仓位数: {strategy.get_max_positions()}")
    
    return strategy

def test_time_windows():
    """测试时间窗口策略"""
    print("\n" + "=" * 60)
    print("⏰ 时间窗口策略测试")
    print("=" * 60)
    
    strategy = SmartArbitrageStrategy()
    
    # 模拟不同时间的策略
    test_times = [
        (0, 6, "周一早上6点 - 黄金时间"),
        (0, 12, "周一中午12点 - 高活跃时间"),
        (0, 20, "周一晚上8点 - 低活跃时间"),
        (2, 14, "周三下午2点 - 高活跃时间"),
        (5, 2, "周六凌晨2点 - 周末交易时间"),
        (6, 10, "周日上午10点 - 周末休市")
    ]
    
    for weekday, hour, description in test_times:
        # 模拟时间
        gmt3_tz = pytz.timezone('Europe/Moscow')
        test_time = datetime.now(gmt3_tz).replace(hour=hour, minute=0)
        
        # 这里需要临时修改策略的时间获取方法来测试
        print(f"\n🕐 {description}:")
        print(f"   时间: 周{'一二三四五六日'[weekday]} {hour:02d}:00")
        
        # 手动判断策略
        if weekday == 0 and 6 <= hour < 7:
            expected_strategy = "golden_hour"
            expected_positions = 4
        elif weekday in [0,1,2,3,4] and hour in [0,12,13,14,15,16]:
            expected_strategy = "balanced"
            expected_positions = 2
        else:
            expected_strategy = "conservative"
            expected_positions = 1
        
        print(f"   预期策略: {expected_strategy}")
        print(f"   预期最大仓位: {expected_positions}")
        
        # 获取对应的阈值
        thresholds = strategy.dynamic_thresholds[expected_strategy]
        print(f"   进场阈值: {thresholds['entry']:.4f}%")
        print(f"   出场阈值: {thresholds['exit']:.4f}%")
        print(f"   策略描述: {thresholds['description']}")

def test_historical_analysis():
    """测试历史数据分析"""
    print("\n" + "=" * 60)
    print("📊 历史数据分析")
    print("=" * 60)
    
    strategy = SmartArbitrageStrategy()
    performance = strategy.analyze_historical_performance()
    
    print("📈 历史表现分析:")
    for key, value in performance.items():
        if isinstance(value, dict):
            print(f"   {key}:")
            for sub_key, sub_value in value.items():
                print(f"     {sub_key}: {sub_value}")
        else:
            print(f"   {key}: {value}")

def main():
    """主测试函数"""
    print("🚀 智能套利系统测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试市场时间检测
        market_open = test_market_timing()
        
        # 2. 测试智能策略
        strategy = test_smart_strategy()
        
        # 3. 测试时间窗口
        test_time_windows()
        
        # 4. 测试历史分析
        test_historical_analysis()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成")
        print("=" * 60)
        
        # 总结
        print("\n📋 测试总结:")
        print(f"   市场状态: {'开放' if market_open else '关闭'}")
        current_info = strategy.get_strategy_info()
        print(f"   当前策略: {current_info['strategy_name']}")
        print(f"   进场阈值: {current_info['entry_threshold']:.4f}%")
        print(f"   出场阈值: {current_info['exit_threshold']:.4f}%")
        print(f"   最大仓位: {current_info['max_positions']}")
        
        print("\n🎯 关键改进:")
        print("   ✅ 基于GMT+3的准确市场时间检测")
        print("   ✅ 基于历史数据的动态阈值策略")
        print("   ✅ 时间窗口优化的仓位管理")
        print("   ✅ 智能进场和出场决策")
        print("   ✅ 周一早上6点黄金时间窗口")
        
        print("\n💡 建议:")
        print("   - 在周一早上6-7点GMT+3时段重点监控")
        print("   - 使用保守策略进行稳定套利")
        print("   - 根据时间窗口调整仓位数量")
        print("   - 持续监控价差分布变化")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
