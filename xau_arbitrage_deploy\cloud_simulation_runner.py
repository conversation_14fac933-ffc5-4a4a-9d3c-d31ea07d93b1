#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
雲端模擬交易運行器
專為Amazon Lightsail等雲端環境設計
"""

import os
import sys
import signal
import time
import logging
from datetime import datetime
from dotenv import load_dotenv

# 確保路徑正確
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
load_dotenv('config.env')

from simulation_trader import XAUSimulationTrader

class CloudSimulationRunner:
    """雲端模擬交易運行器"""
    
    def __init__(self):
        self.simulator = None
        self.running = False
        self.setup_logging()
        
        # 設置信號處理器（優雅關閉）
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
    def setup_logging(self):
        """設置日誌"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('cloud_simulation.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def signal_handler(self, signum, frame):
        """信號處理器（優雅關閉）"""
        self.logger.info(f"收到信號 {signum}，準備優雅關閉...")
        self.running = False
        
        if self.simulator:
            # 保存最終數據
            self.simulator.save_simulation_data()
            self.logger.info("已保存模擬數據")
        
        sys.exit(0)
    
    def run_continuous_simulation(self):
        """運行連續模擬交易"""
        self.logger.info("🚀 啟動雲端模擬交易系統")
        self.logger.info(f"⏰ 開始時間: {datetime.now()}")
        
        try:
            # 創建模擬器
            self.simulator = XAUSimulationTrader()
            self.running = True
            
            # 運行參數
            check_interval = 300  # 5分鐘檢查一次
            save_interval = 3600  # 1小時保存一次
            last_save_time = time.time()
            
            cycle_count = 0
            
            while self.running:
                cycle_count += 1
                current_time = datetime.now()
                
                self.logger.info(f"📊 第 {cycle_count} 次監控循環 ({current_time.strftime('%Y-%m-%d %H:%M:%S')})")
                
                try:
                    # 獲取真實價格
                    current_prices = self.simulator.get_real_prices()
                    if not current_prices:
                        self.logger.warning("無法獲取價格，跳過此次循環")
                        time.sleep(check_interval)
                        continue
                    
                    # 計算價差
                    spread_info = self.simulator.calculate_spread(current_prices['bybit'], current_prices['mt5'])
                    
                    self.logger.info(f"💰 Bybit: ${current_prices['bybit']:.2f}, MT5: ${current_prices['mt5']:.2f}")
                    self.logger.info(f"📈 價差: {spread_info['percentage']:.3f}%, 活躍倉位: {len(self.simulator.active_trades)}/{self.simulator.max_positions}")
                    
                    # 更新績效指標
                    performance = self.simulator.update_performance_metrics(current_prices)
                    self.logger.info(f"💵 總淨值: ${performance['total_equity']:.2f} ({performance['total_return']:+.2f}%)")
                    
                    # 檢查平倉條件
                    trades_to_close = []
                    for trade_id, trade_info in self.simulator.active_trades.items():
                        if self.simulator.check_close_conditions(trade_info, current_prices):
                            trades_to_close.append(trade_id)
                    
                    # 執行平倉
                    for trade_id in trades_to_close:
                        if trade_id in self.simulator.active_trades:
                            close_result = self.simulator.close_arbitrage_trade(self.simulator.active_trades[trade_id], current_prices)
                            if close_result:
                                self.logger.info(f"✅ 平倉交易 {trade_id}, 淨盈虧: ${close_result['net_pnl']:+.2f}")
                    
                    # 檢查開倉條件
                    if (len(self.simulator.active_trades) < self.simulator.max_positions and 
                        abs(spread_info['percentage']) >= self.simulator.min_spread_threshold):
                        
                        self.logger.info(f"🚨 發現套利機會！價差: {spread_info['percentage']:.3f}%")
                        trade_result = self.simulator.execute_arbitrage_trade(spread_info, current_prices)
                        
                        if trade_result:
                            self.logger.info(f"✅ 開倉交易 {trade_result['trade_id']}")
                        else:
                            self.logger.warning("❌ 開倉失敗")
                    
                    # 定期保存數據
                    if time.time() - last_save_time > save_interval:
                        self.simulator.save_simulation_data()
                        self.logger.info("💾 已保存模擬數據")
                        last_save_time = time.time()
                    
                except Exception as e:
                    self.logger.error(f"循環執行錯誤: {e}")
                
                # 等待下次檢查
                if self.running:
                    time.sleep(check_interval)
                    
        except KeyboardInterrupt:
            self.logger.info("用戶中斷程序")
        except Exception as e:
            self.logger.error(f"程序執行錯誤: {e}")
        finally:
            if self.simulator:
                self.simulator.save_simulation_data()
                self.logger.info("程序結束，已保存最終數據")

def main():
    """主函數"""
    print("🎮 XAU套利雲端模擬交易系統")
    print("=" * 60)
    print("⚠️  這是模擬交易系統，使用虛擬帳戶")
    print("✅ 使用真實價格數據和交易邏輯")
    print("🔄 按 Ctrl+C 優雅停止程序")
    print("=" * 60)
    
    runner = CloudSimulationRunner()
    runner.run_continuous_simulation()

if __name__ == "__main__":
    main()
