#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XAUUSD vs XAUTUSDT 價差監控系統
--------------------------------
監控 cTrader 的 XAUUSD 和 Bybit 的 XAUTUSDT 之間的價差
每分鐘記錄收盤價格和價差數據
"""

import os
import time
import pandas as pd
import numpy as np
from datetime import datetime
import requests
import MetaTrader5 as mt5
from bybit_mt5_client import BybitMT5Client

# 創建數據目錄
data_dir = 'data'
os.makedirs(data_dir, exist_ok=True)

class XAUSpreadMonitor:
    def __init__(self):
        self.bybit_mt5_client = BybitMT5Client()
        self.csv_file = f'{data_dir}/xau_spread_data.csv'
        self.log_file = 'xau_spread_monitor.log'
        
        # 初始化 CSV 文件
        if not os.path.exists(self.csv_file):
            df = pd.DataFrame(columns=[
                'timestamp', 'bybit_xautusdt', 'mt5_xauusd', 
                'absolute_spread', 'percentage_spread', 'direction'
            ])
            df.to_csv(self.csv_file, index=False)
    
    def get_bybit_xautusdt_price(self):
        """獲取 Bybit XAUTUSDT 永續合約價格"""
        try:
            url = "https://api.bybit.com/v5/market/tickers"
            params = {
                'category': 'linear',
                'symbol': 'XAUTUSDT'
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('retCode') == 0 and data.get('result', {}).get('list'):
                    ticker = data['result']['list'][0]
                    return float(ticker['lastPrice'])
                else:
                    print(f"Bybit XAUTUSDT 請求失敗: {data}")
                    return None
            else:
                print(f"Bybit XAUTUSDT 請求失敗: {response.status_code}")
                return None
        except Exception as e:
            print(f"獲取 Bybit XAUTUSDT 價格錯誤: {e}")
            return None
    
    def get_mt5_xauusd_price(self):
        """獲取 Bybit MT5 XAUUSD 價格"""
        try:
            # 連接 MT5
            if self.bybit_mt5_client.connect():
                # 嘗試獲取 XAUUSD+ 價格 (Bybit MT5 的黃金代號)
                tick = self.bybit_mt5_client.get_tick("XAUUSD+")
                self.bybit_mt5_client.disconnect()
                
                if tick and tick['bid'] > 0:
                    # 使用買賣價的中間價
                    mid_price = (tick['bid'] + tick['ask']) / 2
                    return float(mid_price)
                else:
                    print("無法獲取 MT5 XAUUSD+ 價格，可能是市場關閉")
            else:
                print("MT5 連接失敗")
        except Exception as e:
            print(f"獲取 MT5 XAUUSD+ 價格錯誤: {e}")
        return None
    
    def calculate_spread(self, bybit_price, mt5_price):
        """計算價差"""
        if bybit_price is None or mt5_price is None:
            return None, None, None
        
        absolute_spread = mt5_price - bybit_price
        percentage_spread = (absolute_spread / bybit_price) * 100
        
        if percentage_spread > 0:
            direction = "MT5 > Bybit"
        else:
            direction = "Bybit > MT5"
        
        return absolute_spread, percentage_spread, direction
    
    def log_spread(self, bybit_price, mt5_price, absolute_spread, percentage_spread, direction):
        """記錄價差到 CSV"""
        try:
            timestamp = datetime.now()
            
            # 讀取現有數據
            try:
                df = pd.read_csv(self.csv_file)
            except:
                # 如果文件不存在或讀取失敗，創建新的 DataFrame
                df = pd.DataFrame(columns=[
                    'timestamp', 'bybit_xautusdt', 'mt5_xauusd', 
                    'absolute_spread', 'percentage_spread', 'direction'
                ])
            
            # 添加新記錄
            new_row = {
                'timestamp': timestamp,
                'bybit_xautusdt': bybit_price,
                'mt5_xauusd': mt5_price,
                'absolute_spread': absolute_spread,
                'percentage_spread': percentage_spread,
                'direction': direction
            }
            
            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
            df.to_csv(self.csv_file, index=False, encoding='utf-8')
            
            # 寫入日誌
            log_message = f"{timestamp}: Bybit XAUTUSDT={bybit_price:.2f}, MT5 XAUUSD={mt5_price:.2f}, 價差={absolute_spread:.2f} ({percentage_spread:.3f}%) [{direction}]"
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_message + '\n')
            
            return True
        except Exception as e:
            print(f"記錄價差錯誤: {e}")
            return False
    
    def print_status(self, bybit_price, mt5_price, absolute_spread, percentage_spread, direction):
        """打印狀態"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"\n[{timestamp}] XAU 價差監控")
        print(f"Bybit XAUTUSDT 永續: {bybit_price:.2f}")
        print(f"Bybit MT5 XAUUSD: {mt5_price:.2f}")
        print(f"絕對價差: {absolute_spread:.2f}")
        print(f"百分比價差: {percentage_spread:.3f}%")
        print(f"方向: {direction}")
        
        # 檢查是否有套利機會
        if abs(percentage_spread) >= 0.2:
            print(f"🚨 套利機會! 價差超過 0.2%")
            if percentage_spread > 0:
                print(f"策略: 賣出 MT5 XAUUSD → 買入 Bybit XAUTUSDT 永續")
            else:
                print(f"策略: 買入 MT5 XAUUSD → 賣出 Bybit XAUTUSDT 永續")
        
        print("-" * 50)
    
    def run_monitor(self, interval=60):
        """運行監控"""
        print(f"開始 XAU 價差監控 (Bybit XAUTUSDT 永續 vs Bybit MT5 XAUUSD)")
        print(f"監控間隔: {interval} 秒")
        print(f"數據保存至: {self.csv_file}")
        print(f"日誌保存至: {self.log_file}")
        print("=" * 60)
        
        try:
            while True:
                # 獲取價格
                bybit_price = self.get_bybit_xautusdt_price()
                mt5_price = self.get_mt5_xauusd_price()
                
                if bybit_price is not None and mt5_price is not None:
                    # 計算價差
                    absolute_spread, percentage_spread, direction = self.calculate_spread(bybit_price, mt5_price)
                    
                    # 記錄價差
                    self.log_spread(bybit_price, mt5_price, absolute_spread, percentage_spread, direction)
                    
                    # 打印狀態
                    self.print_status(bybit_price, mt5_price, absolute_spread, percentage_spread, direction)
                else:
                    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 無法獲取價格數據")
                
                # 等待下次監控
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n監控已停止")
        except Exception as e:
            print(f"監控錯誤: {e}")

def main():
    monitor = XAUSpreadMonitor()
    monitor.run_monitor()

if __name__ == "__main__":
    main() 