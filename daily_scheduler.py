#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日定时任务调度器
每天早上8点发送前一天的交易报告
"""

import schedule
import time
import threading
from datetime import datetime
import logging

from daily_report_generator import DailyReportGenerator

class DailyScheduler:
    def __init__(self):
        self.report_generator = DailyReportGenerator()
        self.running = False
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('daily_scheduler.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def send_daily_report(self):
        """发送每日报告的任务"""
        try:
            self.logger.info("开始执行每日报告任务")
            success = self.report_generator.generate_and_send_report()
            
            if success:
                self.logger.info("✅ 每日报告发送成功")
            else:
                self.logger.error("❌ 每日报告发送失败")
                
        except Exception as e:
            self.logger.error(f"每日报告任务执行失败: {e}")

    def start_scheduler(self):
        """启动定时调度器"""
        self.logger.info("启动每日报告调度器")
        
        # 设置每天早上8点执行
        schedule.every().day.at("08:00").do(self.send_daily_report)
        
        # 也可以设置测试用的更频繁执行（可选）
        # schedule.every(10).minutes.do(self.send_daily_report)  # 每10分钟执行一次（测试用）
        
        self.running = True
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("收到停止信号，正在关闭调度器...")
                self.running = False
                break
            except Exception as e:
                self.logger.error(f"调度器运行错误: {e}")
                time.sleep(60)

    def stop_scheduler(self):
        """停止调度器"""
        self.running = False
        self.logger.info("调度器已停止")

    def run_in_background(self):
        """在后台线程中运行调度器"""
        scheduler_thread = threading.Thread(target=self.start_scheduler, daemon=True)
        scheduler_thread.start()
        self.logger.info("每日报告调度器已在后台启动")
        return scheduler_thread

def main():
    """主函数"""
    scheduler = DailyScheduler()
    
    print("🕐 每日报告调度器启动")
    print("📅 将在每天早上8:00发送前一天的交易报告")
    print("⏹️ 按 Ctrl+C 停止调度器")
    
    try:
        scheduler.start_scheduler()
    except KeyboardInterrupt:
        print("\n👋 调度器已停止")

if __name__ == "__main__":
    main()
