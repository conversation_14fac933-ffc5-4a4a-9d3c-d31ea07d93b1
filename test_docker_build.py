#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Docker构建
"""

import subprocess
import sys
import os

def test_docker_build():
    """测试Docker构建"""
    print("🐳 测试Docker构建...")
    
    try:
        # 检查Docker是否可用
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("❌ Docker未安装或不可用")
            return False
        
        print(f"✅ Docker版本: {result.stdout.strip()}")
        
        # 构建Docker镜像
        print("🔨 开始构建Docker镜像...")
        result = subprocess.run([
            'docker', 'build', '-t', 'xau-arb-test', '.'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Docker镜像构建成功")
            
            # 测试运行容器（干运行模式）
            print("🧪 测试容器运行...")
            result = subprocess.run([
                'docker', 'run', '--rm', '-e', 'DRY_RUN=true',
                '-e', 'BYBIT_API_KEY=test',
                '-e', 'BYBIT_API_SECRET=test',
                '-e', 'TELEGRAM_BOT_TOKEN=test',
                '-e', 'TELEGRAM_CHAT_ID=test',
                'xau-arb-test'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ 容器运行测试成功")
                print(f"输出: {result.stdout[:200]}...")
                return True
            else:
                print(f"❌ 容器运行失败: {result.stderr}")
                return False
        else:
            print(f"❌ Docker构建失败:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Docker操作超时")
        return False
    except FileNotFoundError:
        print("❌ Docker命令未找到，请安装Docker")
        return False
    except Exception as e:
        print(f"❌ Docker测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Docker构建测试")
    print("=" * 50)
    
    success = test_docker_build()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Docker构建测试成功！")
        print("✅ 可以使用Docker部署到任何支持Docker的云平台")
    else:
        print("⚠️ Docker构建测试失败")
        print("💡 建议使用Railway的自动检测功能")

if __name__ == "__main__":
    main()
