# XAU套利模擬交易系統

## 🎯 系統概述

這是一個完全修正的XAU套利模擬交易系統，解決了之前的致命問題：

### 🚨 已修正的關鍵問題

1. **下單邏輯錯誤** ✅ 已修正
   - 原問題：以MT5為基準導致Bybit下單失敗
   - 修正方案：以Bybit為基準，確保盎司數完全匹配

2. **緊急平倉失敗** ✅ 已修正
   - 原問題：MT5連接斷開後無法找到倉位
   - 修正方案：修正連接管理邏輯

3. **風險敞口問題** ✅ 已解決
   - 使用模擬交易避免實際資金風險
   - 使用真實價格數據確保準確性

## 📊 系統特點

### 🎮 模擬交易
- **真實價格數據**：使用Bybit API和MT5連接獲取實時價格
- **模擬執行**：所有交易都是模擬，無實際資金風險
- **真實手續費**：按實際費率計算手續費和滑點
- **完整記錄**：詳細記錄所有交易和績效數據

### 🔧 正確的下單邏輯
```
基準設定：
- Bybit: 1張 = 1盎司黃金 (~$3,300, 保證金~$165 @20x槓桿)
- MT5: 0.01手 = 1盎司黃金 (~$3,300, 保證金~$6.6 @500x槓桿)

確保：
✅ 盎司數完全匹配 (1:1)
✅ 風險完全對沖
✅ 保證金合理分配
```

### 💰 模擬帳戶設置
- **Bybit模擬帳戶**：10,000 USDT，20倍槓桿
- **MT5模擬帳戶**：1,000 USD，500倍槓桿
- **交易參數**：
  - 進場閾值：0.2%
  - 平倉閾值：0.05%
  - 風控閾值：0.5%
  - 最大倉位：5個

## 🚀 使用方法

### 1. 快速測試
```bash
python simulation_trader.py
```

### 2. 自定義運行
```python
from simulation_trader import XAUSimulationTrader

# 創建模擬器
simulator = XAUSimulationTrader()

# 運行模擬（60分鐘，每60秒檢查）
simulator.run_simulation(duration_minutes=60, check_interval=60)
```

### 3. 長期測試
```python
# 運行24小時模擬
simulator.run_simulation(duration_minutes=1440, check_interval=300)
```

## 📈 績效分析

### 實時監控
- 總淨值變化
- 收益率統計
- 活躍倉位數量
- 保證金使用率

### 數據記錄
- `data/simulation_results.json`：完整交易記錄
- `data/simulation_performance.csv`：績效數據
- `simulation_trader.log`：詳細日誌

### 關鍵指標
- 總交易次數
- 勝率統計
- 平均持倉時間
- 最大回撤
- 夏普比率

## 🔍 模擬結果示例

```
📊 模擬交易完成報告
============================================================
🔢 總交易次數: 5
📊 活躍倉位: 5
💰 初始資金: $11,000.00
💵 當前淨值: $10,992.20
📈 總收益率: -0.07%
💵 總收益: $-7.80

📊 Bybit帳戶:
   餘額: $9,990.07
   淨值: $9,989.00
   已用保證金: $827.72

📊 MT5帳戶:
   餘額: $999.50
   淨值: $1,003.20
   已用保證金: $33.19
```

## ⚠️ 重要說明

### 🔒 安全保障
1. **純模擬**：不會執行任何真實交易
2. **真實數據**：使用真實價格確保準確性
3. **完整測試**：可以安全測試各種市場條件

### 📊 數據準確性
- 所有價格數據來自真實API
- 手續費按實際費率計算
- 滑點模擬真實市場條件
- 保證金計算完全準確

### 🎯 使用建議
1. **先模擬測試**：在真實交易前充分測試
2. **觀察績效**：分析不同市場條件下的表現
3. **優化參數**：根據模擬結果調整交易參數
4. **風險評估**：了解最大可能損失

## 🔄 從模擬到實盤

當模擬結果滿意後，可以：
1. 使用相同的交易邏輯
2. 應用相同的風險管理
3. 採用相同的倉位計算
4. 保持相同的紀律性

**模擬系統已完全修正所有問題，可以安全地用於策略驗證和風險評估！** 🎯
