# XAU套利模擬交易系統 - Windows Server部署指南

## 🎯 Windows Server部署步驟

您的Lightsail實例是Windows Server 2022，以下是專門的部署步驟：

### 步驟1: 上傳文件到Windows Server

#### 方法1: 使用scp (推薦)
```bash
# 在您的本地電腦上執行
scp -i "C:\Users\<USER>\Downloads\LightsailDefaultKey-ap-northeast-2.pem" -r ./xau_arbitrage_deploy Administrator@43.202.181.16:C:\xau_arbitrage
```

#### 方法2: 使用RDP遠程桌面
1. 在Lightsail控制台獲取Administrator密碼
2. 使用遠程桌面連接到 43.202.181.16
3. 直接複製文件到 C:\xau_arbitrage

### 步驟2: 連接到Windows Server

#### 使用SSH連接
```bash
ssh -i "C:\Users\<USER>\Downloads\LightsailDefaultKey-ap-northeast-2.pem" Administrator@43.202.181.16
```

#### 或使用RDP
- 地址: 43.202.181.16
- 用戶: Administrator
- 密碼: (從Lightsail控制台獲取)

### 步驟3: 安裝Python (如果未安裝)

1. **下載Python**
   ```powershell
   # 在Windows Server上執行
   Invoke-WebRequest -Uri "https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe" -OutFile "python-installer.exe"
   ```

2. **安裝Python**
   ```powershell
   .\python-installer.exe /quiet InstallAllUsers=1 PrependPath=1
   ```

3. **重啟PowerShell或重新連接**

### 步驟4: 運行部署腳本

```powershell
# 進入項目目錄
cd C:\xau_arbitrage

# 執行部署腳本
PowerShell -ExecutionPolicy Bypass -File .\windows_deploy_setup.ps1
```

### 步驟5: 測試和啟動

1. **測試運行**
   ```powershell
   .\start_simulation.bat
   ```

2. **後台運行**
   ```powershell
   .\start_background.bat
   ```

## 🔧 Windows特定配置

### 防火牆設置
```powershell
# 允許Python通過防火牆
New-NetFirewallRule -DisplayName "Python" -Direction Inbound -Program "C:\xau_arbitrage\venv\Scripts\python.exe" -Action Allow
```

### 設置開機自啟動
1. 創建計劃任務
2. 設置觸發器為"系統啟動時"
3. 操作為運行 `C:\xau_arbitrage\start_background.bat`

## 📊 監控和管理

### 查看運行狀態
```powershell
# 查看Python進程
Get-Process python

# 查看日誌
Get-Content -Path "C:\xau_arbitrage\cloud_simulation.log" -Tail 50 -Wait
```

### 停止程序
```powershell
# 停止所有Python進程
Get-Process python | Stop-Process -Force
```

## 🚨 重要提醒

1. **確保config.env正確設置**
2. **Windows Server需要保持運行狀態**
3. **定期檢查日誌文件**
4. **監控系統資源使用**

## 📞 如果遇到問題

1. **Python安裝問題**: 確保Python已正確安裝並添加到PATH
2. **權限問題**: 以管理員身份運行PowerShell
3. **網絡問題**: 檢查防火牆和安全組設置
4. **API連接問題**: 驗證config.env中的API密鑰
