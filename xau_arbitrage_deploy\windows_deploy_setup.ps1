# XAU套利模擬交易系統 - Windows Server部署腳本

Write-Host "🚀 XAU套利模擬交易系統 - Windows Server部署設置" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Yellow

# 檢查Python是否已安裝
Write-Host "🐍 檢查Python安裝狀態..." -ForegroundColor Cyan
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python已安裝: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python未安裝，請先安裝Python 3.8+" -ForegroundColor Red
    Write-Host "下載地址: https://www.python.org/downloads/" -ForegroundColor Yellow
    exit 1
}

# 檢查pip
Write-Host "📦 檢查pip..." -ForegroundColor Cyan
try {
    $pipVersion = pip --version 2>&1
    Write-Host "✅ pip已安裝: $pipVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ pip未安裝" -ForegroundColor Red
    exit 1
}

# 進入項目目錄
$projectDir = "C:\xau_arbitrage"
if (Test-Path $projectDir) {
    Write-Host "📁 進入項目目錄: $projectDir" -ForegroundColor Cyan
    Set-Location $projectDir
} else {
    Write-Host "❌ 項目目錄不存在: $projectDir" -ForegroundColor Red
    exit 1
}

# 創建虛擬環境
Write-Host "🌐 創建Python虛擬環境..." -ForegroundColor Cyan
python -m venv venv

# 激活虛擬環境並安裝依賴
Write-Host "⚡ 激活虛擬環境並安裝依賴..." -ForegroundColor Cyan
& ".\venv\Scripts\Activate.ps1"
python -m pip install --upgrade pip
pip install -r requirements.txt

# 創建數據目錄
Write-Host "📁 創建數據目錄..." -ForegroundColor Cyan
if (!(Test-Path "data")) {
    New-Item -ItemType Directory -Path "data"
}
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs"
}

# 創建Windows服務腳本
Write-Host "⚙️ 創建Windows服務腳本..." -ForegroundColor Cyan
$serviceScript = @"
import sys
import os
import time
import subprocess
from pathlib import Path

# 設置工作目錄
os.chdir(r'C:\xau_arbitrage')

# 激活虛擬環境並運行
python_exe = r'C:\xau_arbitrage\venv\Scripts\python.exe'
script_path = r'C:\xau_arbitrage\cloud_simulation_runner.py'

while True:
    try:
        print(f"啟動XAU模擬交易系統: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        result = subprocess.run([python_exe, script_path], 
                              capture_output=True, text=True, timeout=None)
        
        if result.returncode != 0:
            print(f"程序異常退出: {result.stderr}")
            time.sleep(60)  # 等待1分鐘後重啟
        else:
            print("程序正常退出")
            break
            
    except Exception as e:
        print(f"啟動失敗: {e}")
        time.sleep(60)
"@

$serviceScript | Out-File -FilePath "windows_service_runner.py" -Encoding UTF8

# 創建啟動批處理文件
Write-Host "📝 創建啟動腳本..." -ForegroundColor Cyan
$startScript = @"
@echo off
cd /d C:\xau_arbitrage
call venv\Scripts\activate.bat
python cloud_simulation_runner.py
pause
"@

$startScript | Out-File -FilePath "start_simulation.bat" -Encoding ASCII

# 創建後台運行批處理文件
$backgroundScript = @"
@echo off
cd /d C:\xau_arbitrage
call venv\Scripts\activate.bat
python windows_service_runner.py
"@

$backgroundScript | Out-File -FilePath "start_background.bat" -Encoding ASCII

Write-Host ""
Write-Host "🎉 Windows Server部署設置完成！" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Yellow
Write-Host "📋 接下來的步驟：" -ForegroundColor Cyan
Write-Host "1. 檢查config.env文件是否正確設置" -ForegroundColor White
Write-Host "2. 測試運行: .\start_simulation.bat" -ForegroundColor White
Write-Host "3. 後台運行: .\start_background.bat" -ForegroundColor White
Write-Host "4. 查看日誌: type cloud_simulation.log" -ForegroundColor White
Write-Host "==================================================" -ForegroundColor Yellow
