#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速測試修正後的模擬交易系統
"""

import os
import sys
from dotenv import load_dotenv

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
load_dotenv('config.env')

from simulation_trader import XAUSimulationTrader

def quick_test():
    """快速測試系統功能"""
    print("🧪 快速測試修正後的模擬交易系統")
    print("=" * 60)
    
    # 創建模擬器
    simulator = XAUSimulationTrader()
    
    # 測試價格獲取
    print("💰 測試價格獲取...")
    prices = simulator.get_real_prices()
    if prices:
        print(f"   ✅ Bybit: ${prices['bybit']:.2f}")
        print(f"   ✅ MT5: ${prices['mt5']:.2f}")
        
        spread = simulator.calculate_spread(prices['bybit'], prices['mt5'])
        print(f"   📈 價差: {spread['percentage']:.3f}%")
        
        # 測試市場狀態
        market_open = simulator.is_market_open()
        print(f"   🕐 市場狀態: {'開放' if market_open else '關閉'}")
        
        # 測試倉位計算
        position_sizes = simulator.calculate_position_sizes(prices)
        if position_sizes:
            print(f"   📊 倉位計算成功")
            print(f"      Bybit: {position_sizes['bybit_qty']} 張")
            print(f"      MT5: {position_sizes['mt5_qty']} 手")
            print(f"      總保證金: ${position_sizes['total_margin']:.2f}")
        
        # 如果價差足夠，測試交易執行
        if abs(spread['percentage']) >= 0.1:  # 降低閾值進行測試
            print(f"\n🚨 測試交易執行...")
            
            # 臨時調整閾值
            original_threshold = simulator.min_spread_threshold
            simulator.min_spread_threshold = 0.1
            
            trade_result = simulator.execute_arbitrage_trade(spread, prices)
            
            if trade_result:
                print(f"   ✅ 開倉成功: {trade_result['trade_id']}")
                
                # 測試平倉
                close_result = simulator.close_arbitrage_trade(trade_result, prices)
                if close_result:
                    print(f"   ✅ 平倉成功，淨盈虧: ${close_result['net_pnl']:+.2f}")
                else:
                    print(f"   ❌ 平倉失敗")
            else:
                print(f"   ❌ 開倉失敗")
            
            # 恢復原始閾值
            simulator.min_spread_threshold = original_threshold
        else:
            print(f"\n⚠️ 價差 {spread['percentage']:.3f}% 不足以測試交易")
        
        print(f"\n✅ 快速測試完成！系統運行正常")
        
    else:
        print("❌ 價格獲取失敗")

if __name__ == "__main__":
    quick_test()
