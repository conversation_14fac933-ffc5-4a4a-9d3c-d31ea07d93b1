#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 MT5 帳戶狀態
"""

import os
import sys
from dotenv import load_dotenv
from cloud_config import get_mt5_client

load_dotenv('config.env')

def check_mt5_account():
    """檢查 MT5 帳戶詳細狀態"""
    print("🔍 檢查 MT5 帳戶狀態...")
    
    # 初始化 MT5 客戶端
    MT5ClientClass = get_mt5_client()
    mt5_client = MT5ClientClass()
    
    # 連接 MT5
    if not mt5_client.connect():
        print("❌ MT5 連接失敗")
        return False
    
    print("✅ MT5 連接成功")
    
    # 獲取帳戶資訊
    account_info = mt5_client.get_account_info()
    if account_info:
        print(f"💰 帳戶餘額: {account_info.get('balance', 0):.2f}")
        print(f"💰 可用餘額: {account_info.get('equity', 0):.2f}")
        print(f"💰 浮動盈虧: {account_info.get('profit', 0):.2f}")
        print(f"💰 保證金: {account_info.get('margin', 0):.2f}")
        print(f"💰 可用保證金: {account_info.get('margin_free', 0):.2f}")
        print(f"💰 保證金水平: {account_info.get('margin_level', 0):.2f}%")
    else:
        print("❌ 無法獲取帳戶資訊")
        return False
    
    # 獲取持倉資訊
    positions = mt5_client.get_positions("XAUUSD+")
    if positions:
        print(f"📊 當前 XAUUSD+ 持倉數量: {len(positions)}")
        for i, pos in enumerate(positions):
            print(f"   持倉 {i+1}: {pos.get('type', 'Unknown')} {pos.get('volume', 0)} 手")
            print(f"   開倉價格: {pos.get('price_open', 0):.2f}")
            print(f"   當前價格: {pos.get('price_current', 0):.2f}")
            print(f"   盈虧: {pos.get('profit', 0):.2f}")
    else:
        print("📊 沒有 XAUUSD+ 持倉")
    
    # 獲取 XAUUSD+ 商品資訊
    symbol_info = mt5_client.get_symbol_info("XAUUSD+")
    if symbol_info:
        print(f"📈 XAUUSD+ 商品資訊:")
        print(f"   最小下單量: {symbol_info.get('volume_min', 0)}")
        print(f"   最大下單量: {symbol_info.get('volume_max', 0)}")
        print(f"   合約大小: {symbol_info.get('trade_contract_size', 0)}")
        print(f"   保證金要求: {symbol_info.get('margin_initial', 0):.2f}")
        print(f"   保證金維持: {symbol_info.get('margin_maintenance', 0):.2f}")
        print(f"   交易模式: {symbol_info.get('trade_mode', 0)}")
        print(f"   允許的filling mode: {symbol_info.get('filling_mode', 0)}")
    else:
        print("❌ 無法獲取 XAUUSD+ 商品資訊")
    
    # 測試小量下單
    print("\n🧪 測試小量下單 (0.005 手)...")
    test_order = mt5_client.place_order(
        symbol="XAUUSD+",
        order_type=1,  # Sell
        volume=0.005,  # 更小的數量
        comment="Test order"
    )
    
    if test_order:
        print("✅ 小量下單成功！")
        print(f"   訂單號: {test_order.get('order', 0)}")
        
        # 立即平倉測試訂單
        print("🔄 立即平倉測試訂單...")
        close_result = mt5_client.close_position(ticket=test_order.get('order', 0))
        if close_result:
            print("✅ 測試訂單平倉成功")
        else:
            print("❌ 測試訂單平倉失敗")
    else:
        print("❌ 小量下單失敗")
    
    mt5_client.disconnect()
    return True

if __name__ == "__main__":
    check_mt5_account() 