#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
準備雲端部署文件
"""

import os
import shutil
import sys

def prepare_deployment():
    """準備部署文件"""
    print("📦 準備XAU套利模擬交易系統雲端部署")
    print("=" * 60)
    
    # 創建部署目錄
    deploy_dir = "xau_arbitrage_deploy"
    if os.path.exists(deploy_dir):
        print(f"🗑️ 清理舊的部署目錄: {deploy_dir}")
        shutil.rmtree(deploy_dir)
    
    os.makedirs(deploy_dir)
    print(f"📁 創建部署目錄: {deploy_dir}")
    
    # 需要複製的文件列表
    files_to_copy = [
        "simulation_trader.py",
        "cloud_simulation_runner.py", 
        "bybit_futures_client.py",
        "cloud_config.py",
        "telegram_notifier.py",
        "config.env",
        "requirements.txt",
        "deploy_setup.sh",
        "DEPLOY_GUIDE.md"
    ]
    
    # 複製文件
    copied_files = []
    missing_files = []
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, deploy_dir)
            copied_files.append(file)
            print(f"✅ 複製: {file}")
        else:
            missing_files.append(file)
            print(f"❌ 缺失: {file}")
    
    # 創建數據目錄
    os.makedirs(os.path.join(deploy_dir, "data"), exist_ok=True)
    print("📁 創建數據目錄: data/")
    
    # 設置執行權限
    setup_script = os.path.join(deploy_dir, "deploy_setup.sh")
    if os.path.exists(setup_script):
        os.chmod(setup_script, 0o755)
        print("🔐 設置deploy_setup.sh執行權限")
    
    runner_script = os.path.join(deploy_dir, "cloud_simulation_runner.py")
    if os.path.exists(runner_script):
        os.chmod(runner_script, 0o755)
        print("🔐 設置cloud_simulation_runner.py執行權限")
    
    print("\n" + "=" * 60)
    print("📊 部署準備報告")
    print("=" * 60)
    print(f"✅ 成功複製文件: {len(copied_files)}")
    for file in copied_files:
        print(f"   - {file}")
    
    if missing_files:
        print(f"\n❌ 缺失文件: {len(missing_files)}")
        for file in missing_files:
            print(f"   - {file}")
        print("\n⚠️ 請確保所有必要文件都存在後重新運行")
        return False
    
    print(f"\n📁 部署目錄: {os.path.abspath(deploy_dir)}")
    print("\n🚀 下一步操作:")
    print("1. 檢查config.env中的API密鑰設置")
    print("2. 使用以下命令上傳到Lightsail:")
    print(f"   scp -i ~/keys/LightsailDefaultKey.pem -r ./{deploy_dir} ubuntu@YOUR_IP:/home/<USER>/xau_arbitrage")
    print("3. SSH連接到實例並運行部署腳本")
    print("4. 參考DEPLOY_GUIDE.md獲取詳細步驟")
    
    return True

if __name__ == "__main__":
    success = prepare_deployment()
    if not success:
        sys.exit(1)
    print("\n✅ 部署準備完成！")
