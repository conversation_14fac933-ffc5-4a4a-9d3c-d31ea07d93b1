#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的 Bybit 下單功能
"""

import os
import sys
from dotenv import load_dotenv
from bybit_futures_client import BybitFuturesClient

load_dotenv('config.env')

def test_bybit_order():
    """測試 Bybit 下單功能"""
    print("🔍 測試修正後的 Bybit 下單功能...")
    
    client = BybitFuturesClient()
    
    # 1. 測試連接和帳戶資訊
    print("\n1. 測試帳戶資訊...")
    account_info = client.get_account_info()
    if account_info:
        print("✅ 帳戶資訊獲取成功")
        wallet_list = account_info.get("list", [])
        if wallet_list:
            wallet = wallet_list[0]
            total_balance = float(wallet.get("totalWalletBalance", 0))
            available_balance = float(wallet.get("totalAvailableBalance", 0))
            print(f"   總餘額: {total_balance:.2f} USDT")
            print(f"   可用餘額: {available_balance:.2f} USDT")
    else:
        print("❌ 帳戶資訊獲取失敗")
        return False
    
    # 2. 測試價格獲取
    print("\n2. 測試價格獲取...")
    price = client.get_ticker_price("XAUTUSDT")
    if price:
        print(f"✅ XAUTUSDT 價格獲取成功: {price:.2f}")
    else:
        print("❌ 價格獲取失敗")
        return False
    
    # 3. 計算下單數量（使用 30% 餘額，20倍槓桿）
    print("\n3. 計算下單數量...")
    balance = float(wallet.get("totalWalletBalance", 0))
    margin_amount = balance * 0.3  # 30% 餘額
    leverage = 20
    nominal_value = margin_amount * leverage  # 名義價值
    qty = nominal_value / price  # 下單數量
    
    print(f"   帳戶餘額: {balance:.2f} USDT")
    print(f"   保證金金額: {margin_amount:.2f} USDT")
    print(f"   槓桿: {leverage}x")
    print(f"   名義價值: {nominal_value:.2f} USDT")
    print(f"   下單數量: {qty:.3f}")
    
    # 確保名義價值 >= 5 USDT
    if nominal_value < 5:
        print("❌ 名義價值小於 5 USDT，無法下單")
        return False
    
    # 4. 測試下單
    print("\n4. 測試下單...")
    print(f"   下單參數: symbol=XAUTUSDT, side=Buy, qty={qty:.3f}, orderType=Market")
    
    result = client.place_order(
        symbol="XAUTUSDT",
        side="Buy",
        qty=qty,
        order_type="Market",
        time_in_force="GTC"
    )
    
    if result:
        print("✅ 下單成功！")
        print(f"   訂單ID: {result.get('orderId', 'N/A')}")
        print(f"   訂單狀態: {result.get('orderStatus', 'N/A')}")
        return True
    else:
        print("❌ 下單失敗")
        return False

if __name__ == "__main__":
    success = test_bybit_order()
    if success:
        print("\n🎉 Bybit 下單功能測試成功！")
    else:
        print("\n💥 Bybit 下單功能測試失敗！")
        sys.exit(1) 